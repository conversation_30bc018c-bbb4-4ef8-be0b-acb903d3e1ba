{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "doctrine/doctrine-migrations-bundle": "^3.4", "lexik/jwt-authentication-bundle": "^3.1", "nelmio/api-doc-bundle": "^5.0", "nelmio/cors-bundle": "^2.5", "symfony/asset": "7.2.*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/runtime": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/validator": "7.2.*", "symfony/yaml": "7.2.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "require-dev": {"doctrine/doctrine-bundle": "^2.14", "doctrine/orm": "^3.3", "symfony/maker-bundle": "^1.62", "symfony/security-bundle": "7.2.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"start": "symfony serve --port=8012", "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}}