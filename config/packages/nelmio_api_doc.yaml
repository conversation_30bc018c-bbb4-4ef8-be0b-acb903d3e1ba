nelmio_api_doc:
    documentation:
        info:
            title: Consultant Management System API
            description: A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and non-working days management with categorized leave system
            version: 1.0.0
            contact:
                name: API Support
                url: https://consultantmanagementsystem.com/support
                email: <EMAIL>
        components:
            securitySchemes:
                Bearer:
                    type: http
                    scheme: bearer
                    bearerFormat: JWT
                    description: 'Enter JWT token in the format: Bearer {token}'
        security:
            - Bearer: []
        tags:
            - name: Authentication
              description: User authentication and token management
            - name: Consultants
              description: Consultant profile management
            - name: Work Time
              description: Work time tracking and management
            - name: Expenses
              description: Expense reporting and management
            - name: Leaves
              description: Non-working days management (Jours non-travaillés)
            - name: Company
              description: Company information management
            - name: Dashboard
              description: Analytics and reporting
            - name: Invoicing
              description: Invoice generation and management
            - name: Clients
              description: Client management and information
            - name: Trips
              description: Business trip planning and tracking
            - name: Expense Categories
              description: Expense category management and hierarchy
            - name: Leave Categories
              description: Leave category management and configuration
    models:
        names:
            - { alias: 'LoginRequest', type: App\Model\Api\Auth\LoginRequest }
            - { alias: 'LoginResponse', type: App\Model\Api\Auth\LoginResponse }
            - { alias: 'UserInfo', type: App\Model\Api\Auth\UserInfo }
            - { alias: 'RegistrationRequest', type: App\Model\Api\Auth\RegistrationRequest }
            - { alias: 'RegistrationResponse', type: App\Model\Api\Auth\RegistrationResponse }
            - { alias: 'RegistrationUserInfo', type: App\Model\Api\Auth\RegistrationUserInfo }
            - { alias: 'Consultant', type: App\Model\Api\Consultant\ConsultantModel }
    areas:
        default:
            path_patterns:
                - ^/api/v1/auth
                - ^/api/v1/consultants
                - ^/api/v1/work-time
                - ^/api/v1/expenses
                - ^/api/v1/leaves
                - ^/api/v1/company
                - ^/api/v1/dashboard
                - ^/api/v1/clients
                - ^/api/v1/trips
                - ^/api/v1/expense-categories
                - ^/api/v1/leave-categories
