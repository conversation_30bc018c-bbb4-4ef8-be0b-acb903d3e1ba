openapi: 3.0.0
info:
  title: Consultant Management API
  description: API for managing consultants, work time, expenses, and leaves
  version: 1.0.0
servers:
  - url: /api/v1
    description: API v1
tags:
  - name: Home
    description: API information endpoints
  - name: Consultants
    description: Consultant management endpoints
  - name: Work Time
    description: Work time tracking endpoints
  - name: Expenses
    description: Expense management endpoints
  - name: Leaves
    description: Leave management endpoints
  - name: Company
    description: Company information endpoints
  - name: Dashboard
    description: Dashboard data endpoints
  - name: Authentication
    description: Authentication endpoints
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - Bearer: []
