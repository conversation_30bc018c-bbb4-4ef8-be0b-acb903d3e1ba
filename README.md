# Consultant Management System

This project is a Symfony backend API for managing consultants, work time, expenses, and leaves.

## Prerequisites

- PHP 8.2 or higher
- Composer
- MySQL or PostgreSQL
- Symfony CLI (recommended for development)

## Installation

1. Clone the repository
```bash
git clone https://github.com/fsli-group/erp-backend
cd erp-backend
```

2. Install dependencies
```bash
composer install
```

3. Configure the database in `.env.local`
```
DATABASE_URL="mysql://user:password@127.0.0.1:3306/mbs?serverVersion=8.0"
```

4. Create the database and run migrations
```bash
php bin/console doctrine:database:create
php bin/console doctrine:migrations:migrate
```

5. Generate JWT keys (if not already done)
```bash
php bin/console lexik:jwt:generate-keypair
```

6. Start the development server
```bash
symfony serve
```

## Features

### Authentication
- User registration and login
- JWT authentication
- Role management (admin/consultant)

### Consultant Management
- Creation, modification, and deletion of consultants
- Assignment of administrator roles

### Work Time Management
- Recording of worked hours
- Remote work/office work distinction
- Activity tracking

### Expense Management
- Recording of expenses with enhanced categorization
- Client and trip association for better organization
- Automatic currency conversion
- Structured expense categories with hierarchical support
- Receipt management and tracking
- Expense validation by administrators (optional)

### Leave Management
- Leave requests by consultants
- Approval/rejection by administrators
- Automatic calculation of remaining days

### Client Management
- Client registration and management
- Client contact information and details
- Active/inactive status management

### Trip Management
- Business trip planning and tracking
- Trip association with clients and consultants
- Expense grouping by trips
- Trip duration and destination tracking

### Enhanced Expense Categories
- Hierarchical category structure (parent/child categories)
- Pre-configured expense categories:
  - Transportation (Flight, Train, Taxi, Car Rental, Parking)
  - Accommodation (Hotel, Airbnb)
  - Meals & Entertainment (Business Meals, Travel Meals)
  - Office & Supplies (Stationery, Software)
  - Communication (Mobile Phone, Internet)
  - Other miscellaneous expenses
- Receipt requirement configuration per category
- Category-based expense reporting

### Dashboard
- Statistics for consultants
- Global view for administrators

## API Endpoints with examples

### Authentication

#### User Registration
- **Endpoint**: `POST /api/register`
- **Description**: Creates a new standard user account in the system. This endpoint is accessible without authentication and is the entry point for new users. The created user will have the ROLE_USER role by default.
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "message": "User created successfully"
  }
  ```

#### User Login
- **Endpoint**: `POST /api/login`
- **Description**: Allows an existing user to authenticate and obtain a JWT token. This token must be included in subsequent requests to access protected resources. The token has a limited validity period (typically 1 hour) and contains information about the user and their roles.
- **Request Body**:
  ```json
  {
    "username": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "user": {
      "email": "<EMAIL>",
      "roles": ["ROLE_USER"]
    }
  }
  ```

#### Consultant Login
- **Endpoint**: `POST /api/login`
- **Description**: Allows an existing consultant to authenticate and obtain a JWT token. This token will contain specific information about the consultant (ID, last name, first name, admin status) as well as their roles. This token will be required to access consultant-specific features.
- **Request Body**:
  ```json
  {
    "username": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "lastName": "Smith",
      "firstName": "John",
      "isAdmin": false
    },
    "roles": ["ROLE_USER"]
  }
  ```

### Consultants

#### List All Consultants (admin only)
- **Endpoint**: `GET /api/consultants`
- **Description**: Retrieves the complete list of all consultants registered in the system. This endpoint is reserved for administrators and returns detailed information about each consultant, including their ID, email, last name, first name, phone number, and admin status. This information is useful for consultant management and task assignment.
- **Response**:
  ```json
  [
    {
      "id": 1,
      "email": "<EMAIL>",
      "lastName": "Smith",
      "firstName": "John",
      "phone": "**********",
      "isAdmin": false
    },
    {...}
  ]
  ```

#### Get Consultant Details
- **Endpoint**: `GET /api/consultants/{id}`
- **Description**: Retrieves detailed information about a specific consultant identified by their ID. A consultant can access their own information, while an administrator can access information about any consultant. The returned information includes the consultant's ID, email, last name, first name, phone number, and admin status.
- **Response**:
  ```json
  {
    "id": 1,
    "email": "<EMAIL>",
    "lastName": "Smith",
    "firstName": "John",
    "phone": "**********",
    "isAdmin": false
  }
  ```

#### Create Consultant (admin only)
- **Endpoint**: `POST /api/consultants`
- **Description**: Allows an administrator to create a new consultant. This is the only endpoint for consultant registration, managed exclusively by administrators. This endpoint requires administrator rights (ROLE_ADMIN) and allows creating accounts with detailed information such as email, password, last name, first name, phone number. The administrator can also decide if the consultant will have administrator rights via the isAdmin parameter.
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "lastName": "Johnson",
    "firstName": "Sarah",
    "phone": "**********",
    "isAdmin": false
  }
  ```
- **Response**:
  ```json
  {
    "message": "Consultant created successfully",
    "id": 2
  }
  ```

#### Update Consultant
- **Endpoint**: `PUT /api/consultants/{id}`
- **Description**: Allows updating information about an existing consultant. A consultant can modify their own information (except their admin status), while an administrator can modify all information about any consultant, including their admin status. Fields not specified in the request will remain unchanged, allowing partial updates.
- **Request Body**:
  ```json
  {
    "phone": "**********",
    "isAdmin": true
  }
  ```
- **Response**:
  ```json
  {
    "message": "Consultant updated successfully",
    "id": 1
  }
  ```

#### Delete Consultant (admin only)
- **Endpoint**: `DELETE /api/consultants/{id}`
- **Description**: Allows an administrator to permanently delete a consultant from the system. This operation is irreversible and will also delete all data associated with the consultant (work time, expenses, leaves). Only administrators can perform this operation, and it is recommended to use it with caution.
- **Response**:
  ```json
  {
    "message": "Consultant deleted successfully"
  }
  ```

#### Current Consultant Profile
- **Endpoint**: `GET /api/consultants/me`
- **Description**: Allows an authenticated consultant to retrieve their own information without having to specify their ID. This endpoint uses the JWT token to identify the connected consultant and returns their personal information. It's a convenient way for the client application to retrieve the current user's information after authentication.
- **Response**:
  ```json
  {
    "id": 1,
    "email": "<EMAIL>",
    "lastName": "Smith",
    "firstName": "John",
    "isAdmin": false
  }
  ```

### Company

#### Company Information
- **Endpoint**: `GET /api/company`
- **Description**: Retrieves the company's configuration information, accessible to all consultants. This information includes company details (name, address, phone, email, website, SIRET) as well as important configuration parameters such as the number of annual leave days, work hours per day, and maximum remote work percentage allowed. These parameters are used by the system to perform various calculations and checks.
- **Response**:
  ```json
  {
    "id": 1,
    "name": "MBS Consulting",
    "address": "123 Main Street, New York",
    "phone": "**********",
    "email": "<EMAIL>",
    "website": "https://mbs-consulting.com",
    "siret": "12345678900012",
    "annualLeaveDays": 25,
    "workHoursPerDay": 7.5,
    "maxRemoteWorkPercentage": 60.0
  }
  ```

#### Update Company Information (admin only)
- **Endpoint**: `PUT /api/company`
- **Description**: Allows an administrator to update the company's information and configuration parameters. This endpoint is essential for the initial system configuration and for subsequent adjustments to company parameters. Changes made here will affect system behavior, particularly leave calculation, work time management, and remote work rules.
- **Request Body**:
  ```json
  {
    "name": "MBS Consulting",
    "address": "123 Main Street, New York",
    "phone": "**********",
    "email": "<EMAIL>",
    "website": "https://mbs-consulting.com",
    "siret": "12345678900012",
    "annualLeaveDays": 25,
    "workHoursPerDay": 7.5,
    "maxRemoteWorkPercentage": 60.0
  }
  ```
- **Response**:
  ```json
  {
    "message": "Company updated successfully",
    "id": 1
  }
  ```

### Work Time

#### List All Work Times (admin only)
- **Endpoint**: `GET /api/work-time`
- **Description**: Allows an administrator to retrieve the complete list of all work times recorded by all consultants. This endpoint provides a global view of company activity, allowing administrators to track the work time of all consultants, including hours worked, remote work status, and activities performed.

#### Work Time Details
- **Endpoint**: `GET /api/work-time/{id}`
- **Description**: Retrieves detailed information about a specific work time record. A consultant can access only their own records, while an administrator can access any record. The returned information includes the date, hours worked, remote work status, activity performed, and any comments.

#### Consultant Work Times
- **Endpoint**: `GET /api/work-time/consultant/{consultantId}`
- **Description**: Retrieves all work time records for a specific consultant. A consultant can access only their own records, while an administrator can access records for any consultant. This endpoint is particularly useful for displaying a consultant's work time history and can be filtered by period.
- **Optional Parameters**: `start` and `end` to filter by period (format YYYY-MM-DD)

#### Create Work Time
- **Endpoint**: `POST /api/work-time`
- **Description**: Allows a consultant to record new work time. This endpoint is essential for work time tracking and allows consultants to declare their hours worked, specify whether they were working remotely or not, indicate the activity performed, and add comments. A consultant can only record work time for themselves, while an administrator can do so for any consultant.
- **Request Body**:
  ```json
  {
    "consultantId": 1,
    "date": "2023-05-15",
    "hours": 7.5,
    "remoteWork": true,
    "activity": "Development",
    "comment": "Working on the MBS project"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Work time created successfully",
    "id": 1
  }
  ```

#### Update Work Time
- **Endpoint**: `PUT /api/work-time/{id}`
- **Description**: Allows updating an existing work time record. A consultant can modify only their own records, while an administrator can modify any record. This feature is useful for correcting errors or updating information after the fact. Fields not specified in the request will remain unchanged.
- **Request Body**:
  ```json
  {
    "hours": 8.0,
    "remoteWork": false,
    "comment": "Updated comment"
  }
  ```

#### Delete Work Time
- **Endpoint**: `DELETE /api/work-time/{id}`
- **Description**: Allows permanently deleting a work time record. A consultant can delete only their own records, while an administrator can delete any record. This operation is irreversible and should be used with caution, especially for records that have already been taken into account in calculations or reports.

#### Work Time Statistics
- **Endpoint**: `GET /api/work-time/statistics/{consultantId}`
- **Description**: Calculates and returns detailed statistics about a consultant's work time for a given period. These statistics include the total number of hours worked, the number of remote work hours, and the remote work percentage. A consultant can access only their own statistics, while an administrator can access statistics for any consultant. This information is essential for work time tracking and compliance with remote work rules.
- **Parameters**: `start` and `end` to define the period (format YYYY-MM-DD, required)
- **Response**:
  ```json
  {
    "totalHours": 37.5,
    "remoteHours": 15.0,
    "remoteWorkPercentage": 40.0,
    "period": {
      "start": "2023-05-01",
      "end": "2023-05-31"
    }
  }
  ```

### Expenses

#### List All Expenses (admin only)
- **Endpoint**: `GET /api/expenses`
- **Description**: Allows an administrator to retrieve the complete list of all expenses recorded by all consultants. This endpoint provides a global view of company expenses, allowing administrators to track and manage expenses for all consultants. The returned information includes expense details, amount, currency, euro equivalent, and validation status.

#### List Non-Validated Expenses (admin only)
- **Endpoint**: `GET /api/expenses/non-validated`
- **Description**: Allows an administrator to retrieve the list of all expenses that have not yet been validated. This endpoint is particularly useful for administrators who need to review and approve expenses submitted by consultants. It allows quickly filtering expenses that require attention and validation.

#### Expense Details
- **Endpoint**: `GET /api/expenses/{id}`
- **Description**: Retrieves detailed information about a specific expense. A consultant can access only their own expenses, while an administrator can access any expense. The returned information includes the date, description, amount, currency, amount converted to euros, exchange rate used, receipt, and validation status of the expense.

#### Consultant Expenses
- **Endpoint**: `GET /api/expenses/consultant/{consultantId}`
- **Description**: Retrieves all expenses recorded by a specific consultant. A consultant can access only their own expenses, while an administrator can access expenses for any consultant. This endpoint is particularly useful for displaying a consultant's expense history and can be filtered by period to facilitate search and analysis.
- **Optional Parameters**: `start` and `end` to filter by period (format YYYY-MM-DD)

#### Create Expense
- **Endpoint**: `POST /api/expenses`
- **Description**: Allows a consultant to record a new professional expense with enhanced categorization and trip/client association. This endpoint supports linking expenses to specific clients, trips, and categories for better organization and reporting. The system automatically converts the amount to euros using the currency conversion service.
- **Request Body**:
  ```json
  {
    "consultantId": 1,
    "clientId": 1,
    "tripId": 1,
    "categoryId": 5,
    "date": "2023-05-15",
    "description": "Flight ticket to client meeting",
    "amount": 450.00,
    "currency": "EUR",
    "receipt": "flight-ticket-123.pdf"
  }
  ```
- **Response**:
  ```json
  {
    "id": 1,
    "date": "2023-05-15",
    "description": "Flight ticket to client meeting",
    "amount": "450.00",
    "currency": "EUR",
    "amountEur": "450.00",
    "exchangeRate": "1.0000",
    "receipt": "flight-ticket-123.pdf",
    "validated": false,
    "consultant": {
      "id": 1,
      "firstName": "John",
      "lastName": "Smith"
    },
    "client": {
      "id": 1,
      "name": "Acme Corporation",
      "code": "ACME"
    },
    "trip": {
      "id": 1,
      "title": "Client Meeting in Paris",
      "destination": "Paris, France"
    },
    "category": {
      "id": 5,
      "name": "Flight Tickets",
      "code": "FLIGHT"
    }
  }
  ```

#### Update Expense
- **Endpoint**: `PUT /api/expenses/{id}`
- **Description**: Allows updating an existing expense. A consultant can modify only their own expenses (and only if they have not yet been validated), while an administrator can modify any expense. This feature is useful for correcting errors or updating information after the fact. If the amount or currency is modified, the system will automatically recalculate the amount in euros.
- **Request Body**:
  ```json
  {
    "description": "Client lunch - updated",
    "amount": 50.00
  }
  ```

#### Delete Expense
- **Endpoint**: `DELETE /api/expenses/{id}`
- **Description**: Allows permanently deleting an expense. A consultant can delete only their own expenses and only if they have not yet been validated, while an administrator can delete any expense. This operation is irreversible and should be used with caution, especially for expenses that have already been taken into account in calculations or reports.

#### Validate Expense (admin only)
- **Endpoint**: `PUT /api/expenses/{id}/validate`
- **Description**: Allows an administrator to validate an expense submitted by a consultant. Validating an expense indicates that it has been reviewed and approved by an administrator. Once validated, an expense can no longer be modified or deleted by the consultant who submitted it. This endpoint is essential for the expense approval process within the company.
- **Response**:
  ```json
  {
    "message": "Expense validated successfully",
    "id": 1
  }
  ```

#### Total Consultant Expenses
- **Endpoint**: `GET /api/expenses/total/{consultantId}`
- **Description**: Calculates and returns the total amount of expenses for a consultant for a given period. This total is calculated in euros, regardless of the original currency of the expenses. A consultant can access only their own expense total, while an administrator can access the expense total for any consultant. This endpoint is particularly useful for financial reports and expense tracking.
- **Parameters**: `start` and `end` to define the period (format YYYY-MM-DD, required)
- **Response**:
  ```json
  {
    "totalEur": 245.50,
    "period": {
      "start": "2023-05-01",
      "end": "2023-05-31"
    }
  }
  ```

### Clients

#### List All Clients
- **Endpoint**: `GET /api/v1/clients`
- **Description**: Retrieves the list of all clients in the system. Can be filtered by active status.
- **Optional Parameters**: `active` (boolean) to filter by active status
- **Response**:
  ```json
  [
    {
      "id": 1,
      "name": "Acme Corporation",
      "code": "ACME",
      "isActive": true,
      "address": "123 Business St, New York, NY",
      "phone": "+1234567890",
      "email": "<EMAIL>",
      "website": "https://acme.com"
    }
  ]
  ```

#### Get Client Details
- **Endpoint**: `GET /api/v1/clients/{id}`
- **Description**: Retrieves detailed information about a specific client.

#### Create Client (admin only)
- **Endpoint**: `POST /api/v1/clients`
- **Description**: Allows an administrator to create a new client.
- **Request Body**:
  ```json
  {
    "name": "New Client Corp",
    "code": "NEWCLIENT",
    "address": "456 Business Ave",
    "phone": "+1987654321",
    "email": "<EMAIL>",
    "website": "https://newclient.com",
    "isActive": true
  }
  ```

### Trips

#### List All Trips
- **Endpoint**: `GET /api/v1/trips`
- **Description**: Retrieves trips for the authenticated consultant or all trips for administrators. Can be filtered by date range.
- **Optional Parameters**: `start` and `end` (date format: Y-m-d) to filter by date range
- **Response**:
  ```json
  [
    {
      "id": 1,
      "title": "Client Meeting in Paris",
      "destination": "Paris, France",
      "purpose": "Client consultation",
      "startDate": "2023-06-15",
      "endDate": "2023-06-17",
      "notes": "Important project kickoff",
      "totalExpenses": 1250.50,
      "durationInDays": 3,
      "consultant": {
        "id": 1,
        "firstName": "John",
        "lastName": "Smith"
      },
      "client": {
        "id": 1,
        "name": "Acme Corporation",
        "code": "ACME"
      }
    }
  ]
  ```

#### Get Trip Details
- **Endpoint**: `GET /api/v1/trips/{id}`
- **Description**: Retrieves detailed information about a specific trip.

### Expense Categories

#### List All Expense Categories
- **Endpoint**: `GET /api/v1/expense-categories`
- **Description**: Retrieves all expense categories with hierarchical structure.
- **Optional Parameters**:
  - `active` (boolean) to filter by active status
  - `root_only` (boolean) to get only root categories
- **Response**:
  ```json
  [
    {
      "id": 1,
      "name": "Transportation",
      "code": "TRANSPORT",
      "description": "All transportation related expenses",
      "requiresReceipt": true,
      "isActive": true,
      "fullPath": "Transportation",
      "isRootCategory": true,
      "parentCategory": null,
      "subcategories": [
        {
          "id": 2,
          "name": "Flight Tickets",
          "code": "FLIGHT"
        },
        {
          "id": 3,
          "name": "Train Tickets",
          "code": "TRAIN"
        }
      ]
    }
  ]
  ```

#### Get Category Details
- **Endpoint**: `GET /api/v1/expense-categories/{id}`
- **Description**: Retrieves detailed information about a specific expense category.

#### Get Subcategories
- **Endpoint**: `GET /api/v1/expense-categories/{id}/subcategories`
- **Description**: Retrieves all subcategories of a parent category.

### Leaves

#### List All Leaves (admin only)
- **Endpoint**: `GET /api/leaves`
- **Description**: Allows an administrator to retrieve the complete list of all leave requests submitted by all consultants. This endpoint provides a global view of leaves within the company, allowing administrators to track and manage absences for all consultants. The returned information includes start and end dates, number of days, leave type, comments, and request status.

#### List Pending Leaves (admin only)
- **Endpoint**: `GET /api/leaves/pending`
- **Description**: Allows an administrator to retrieve the list of all leave requests that are pending validation. This endpoint is particularly useful for administrators who need to review and approve or reject leave requests submitted by consultants. It allows quickly filtering requests that require attention and a decision.

#### Leave Details
- **Endpoint**: `GET /api/leaves/{id}`
- **Description**: Retrieves detailed information about a specific leave request. A consultant can access only their own leave requests, while an administrator can access any request. The returned information includes start and end dates, number of days, leave type, comments, and request status.

#### Consultant Leaves
- **Endpoint**: `GET /api/leaves/consultant/{consultantId}`
- **Description**: Retrieves all leave requests submitted by a specific consultant. A consultant can access only their own leave requests, while an administrator can access requests for any consultant. This endpoint is particularly useful for displaying a consultant's leave history and can be filtered by period to facilitate search and analysis.
- **Optional Parameters**: `start` and `end` to filter by period (format YYYY-MM-DD)

#### Create Leave
- **Endpoint**: `POST /api/leaves`
- **Description**: Allows a consultant to submit a new leave request. This endpoint is essential for absence management and allows consultants to specify start and end dates, number of days, leave type, and add comments. The system automatically checks if the consultant has a sufficient leave balance before accepting the request. By default, requests are created with "pending" status and must be approved by an administrator.
- **Request Body**:
  ```json
  {
    "consultantId": 1,
    "startDate": "2023-07-10",
    "endDate": "2023-07-14",
    "numberOfDays": 5,
    "type": "paid leave",
    "comment": "Summer vacation"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Leave request created successfully",
    "id": 1
  }
  ```

#### Update Leave
- **Endpoint**: `PUT /api/leaves/{id}`
- **Description**: Allows updating an existing leave request. A consultant can modify only their own requests and only if they are still pending validation, while an administrator can modify any request. This feature is useful for correcting errors or adjusting dates after the fact. If the number of days is modified, the system will check again if the consultant has a sufficient leave balance.
- **Request Body**:
  ```json
  {
    "startDate": "2023-07-17",
    "endDate": "2023-07-21",
    "numberOfDays": 5
  }
  ```

#### Delete Leave
- **Endpoint**: `DELETE /api/leaves/{id}`
- **Description**: Allows permanently deleting a leave request. A consultant can delete only their own requests and only if they are still pending validation, while an administrator can delete any request. This operation is irreversible and should be used with caution, especially for requests that have already been taken into account in schedules or reports.

#### Approve Leave (admin only)
- **Endpoint**: `PUT /api/leaves/{id}/approve`
- **Description**: Allows an administrator to approve a leave request submitted by a consultant. Approving a request changes its status from "pending" to "approved" and confirms that the leave has been granted. Once approved, a request can no longer be modified or deleted by the consultant who submitted it. This endpoint is essential for the leave validation process within the company.
- **Response**:
  ```json
  {
    "message": "Leave request approved successfully",
    "id": 1
  }
  ```

#### Reject Leave (admin only)
- **Endpoint**: `PUT /api/leaves/{id}/reject`
- **Description**: Allows an administrator to reject a leave request submitted by a consultant. Rejecting a request changes its status from "pending" to "rejected" and indicates that the leave has not been granted. The administrator can specify a reason for rejection that will be added to the request comments. A rejected request can no longer be modified by the consultant, but they can submit a new request.
- **Request Body**:
  ```json
  {
    "reason": "High activity period"
  }
  ```
- **Response**:
  ```json
  {
    "message": "Leave request rejected",
    "id": 1
  }
  ```

#### Consultant Leave Balance
- **Endpoint**: `GET /api/leaves/balance/{consultantId}`
- **Description**: Calculates and returns the number of remaining leave days for a consultant for the current year. This calculation takes into account the total number of annual leave days defined in the company parameters and subtracts days already taken or approved. A consultant can access only their own leave balance, while an administrator can access the leave balance for any consultant. This information is essential for leave planning.
- **Response**:
  ```json
  {
    "remainingDays": 20.0,
    "year": 2023
  }
  ```

### Dashboard

#### Consultant Dashboard
- **Endpoint**: `GET /api/dashboard/consultant/{consultantId}`
- **Description**: Generates and returns a complete dashboard for a specific consultant, grouping all important information in one place. This dashboard includes the consultant's personal information, work time statistics (total hours, remote work hours, remote work percentage), total expenses, and remaining leave balance. A consultant can access only their own dashboard, while an administrator can access the dashboard for any consultant. This endpoint is particularly useful for providing an overview of a consultant's activity.
- **Optional Parameters**: `start` and `end` to define the period (format YYYY-MM-DD, default is the current month)
- **Response**:
  ```json
  {
    "consultant": {
      "id": 1,
      "lastName": "Smith",
      "firstName": "John",
      "email": "<EMAIL>"
    },
    "period": {
      "start": "2023-05-01",
      "end": "2023-05-31"
    },
    "workTime": {
      "totalHours": 160.0,
      "remoteHours": 80.0,
      "remoteWorkPercentage": 50.0
    },
    "expenses": {
      "totalEur": 245.50
    },
    "leaves": {
      "remainingDays": 20.0,
      "year": 2023
    }
  }
  ```

#### Admin Dashboard (admin only)
- **Endpoint**: `GET /api/dashboard/admin`
- **Description**: Generates and returns a global dashboard for administrators, providing an overview of the activity of all consultants. This dashboard includes global statistics (total number of consultants, total hours worked, remote work hours, remote work percentage, total expenses) as well as individual statistics for each consultant. This endpoint is reserved for administrators and is particularly useful for tracking and analyzing company activity as a whole.
- **Optional Parameters**: `start` and `end` to define the period (format YYYY-MM-DD, default is the current month)
- **Response**:
  ```json
  {
    "period": {
      "start": "2023-05-01",
      "end": "2023-05-31"
    },
    "global": {
      "consultantCount": 3,
      "totalHours": 480.0,
      "remoteHours": 240.0,
      "remoteWorkPercentage": 50.0,
      "totalExpenses": 750.25
    },
    "consultants": [
      {
        "consultant": {
          "id": 1,
          "lastName": "Smith",
          "firstName": "John"
        },
        "totalHours": 160.0,
        "remoteHours": 80.0,
        "remoteWorkPercentage": 50.0,
        "totalExpenses": 245.50
      },
      {...}
    ]
  }
  ```

## Additional Configuration

### Currency Conversion API
To use automatic currency conversion, you need to configure an API key in the `.env.local` file:
```
CURRENCY_API_KEY=your_api_key
```

## Development

### Creating an Administrator
To create an initial administrator, you can use the following command:
```bash
php bin/console app:create-admin <EMAIL> password Admin Admin
```
The parameters are:
- `<EMAIL>`: the administrator's email address
- `password`: the administrator's password
- `Admin`: the administrator's last name (optional, default is "Admin")
- `Admin`: the administrator's first name (optional, default is "Admin")

### Seeding Sample Data
To populate the database with sample clients and expense categories, run:
```bash
php bin/console app:seed-expense-data
```
This command will create:
- 5 sample clients (Acme Corporation, TechStart Inc, Global Solutions Ltd, Swiss Finance AG, Nordic Consulting)
- Hierarchical expense categories with subcategories:
  - Transportation (Flight, Train, Taxi, Car Rental, Parking)
  - Accommodation (Hotel, Airbnb)
  - Meals & Entertainment (Business Meals, Travel Meals)
  - Office & Supplies (Stationery, Software)
  - Communication (Mobile Phone, Internet)
  - Other miscellaneous expenses

### Using with Postman

1. **Create an administrator** using the command above

2. **Login as administrator**:
   - Method: `POST`
   - URL: `https://localhost:8012/api/login`
   - Headers:
     - Key: `Content-Type`
     - Value: `application/json`
   - Body (raw JSON):
     ```json
     {
       "username": "<EMAIL>",
       "password": "password"
     }
     ```
   - Response: JWT token to use for subsequent requests

3. **Use the token for authenticated requests**:
   - Add an authorization header:
     - Key: `Authorization`
     - Value: `Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...` (your token)

4. **Test the endpoints** following the examples in this document
