{% extends '@!NelmioApiDoc/SwaggerUi/index.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('css/swagger-custom.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Consultant Management System API Documentation - Interactive API reference for developers">
{% endblock %}

{% block html_attributes %}class="custom-swagger-theme"{% endblock %}

{% block header %}
    <div class="custom-swagger-header">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div class="header-top">
                <h1><i class="fas fa-book"></i> Consultant Management System API</h1>
                <div class="version-badges">
                    <p class="version-badge">API 1.0.0</p>
                    <p class="version-badge">OAS 3.0</p>
                </div>
            </div>
            <p class="header-description">A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management</p>

            <div class="header-info">
                <div class="header-info-section">
                    <h3><i class="fas fa-info-circle"></i> About</h3>
                    <p>This API provides efficient consultant management, time tracking, expense reporting, and leave management capabilities.</p>
                </div>
                <div class="header-info-section">
                    <h3><i class="fas fa-link"></i> Quick Links</h3>
                    <div class="header-links">
                        <a href="{{ path('app_home') }}"><i class="fas fa-home"></i> Home</a>
                        <a href="#" onclick="authorizeModal()"><i class="fas fa-lock"></i> Authenticate</a>
                    </div>
                </div>
                <div class="header-info-section">
                    <h3><i class="fas fa-code"></i> API Details</h3>
                    <p class="base-url"><code>Base URL: /api/v1</code></p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block swagger_ui %}
    {{ parent() }}
    <div class="custom-swagger-footer">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <p>&copy; {{ "now"|date("Y") }} FSLI Group. All rights reserved.</p>
            <div style="margin-top: 8px;">
                <a href="{{ path('app_home') }}">Home</a>
                <a href="#">Terms of Service</a>
                <a href="#">Privacy Policy</a>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function authorizeModal() {
            // Find and click the authorize button
            const authorizeBtn = document.querySelector('.swagger-ui .auth-wrapper .authorize');
            if (authorizeBtn) {
                authorizeBtn.click();
            }
        }

        // Add custom initialization after Swagger UI loads
        window.addEventListener('load', function() {
            // Add some delay to ensure Swagger UI is fully loaded
            setTimeout(function() {
                // Expand all operations by default
                const expandButtons = document.querySelectorAll('.opblock-tag');
                expandButtons.forEach(function(button) {
                    if (button.getAttribute('aria-expanded') === 'false') {
                        button.click();
                    }
                });

                // Hide the redundant info section under the header
                const infoSection = document.querySelector('.swagger-ui .info');
                if (infoSection) {
                    infoSection.style.display = 'none';
                }

                // Fine-tune the body padding based on actual header height
                const header = document.querySelector('.custom-swagger-header');

                if (header) {
                    // Get the header height
                    const headerHeight = header.offsetHeight;

                    // Adjust body padding to match exact header height
                    document.body.style.paddingTop = headerHeight + 'px';
                }
            }, 1000);
        });
    </script>
{% endblock %}
