{% extends 'base.html.twig' %}

{% block title %}Consultant Management System API{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('css/home.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Consultant Management System API - A comprehensive solution for managing consultants, work time, expenses, and leaves">
{% endblock %}

{% block body %}
    <header>
        <div class="container">
            <div class="header-content">
                <h1>Consultant Management System API</h1>
                <p>A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management</p>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="card">
                <h2 class="card-title">Welcome to the Consultant Management System API</h2>
                <p>This API provides a robust set of endpoints for managing all aspects of consultant operations, including profile management, work time tracking, expense reporting, leave management, and comprehensive dashboard analytics.</p>

                <div class="features">
                    <div class="feature">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>Secure Authentication</h3>
                        <p>JWT-based authentication system with role-based access control</p>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3>RESTful Design</h3>
                        <p>Follows REST principles with consistent resource-oriented URLs</p>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3>Comprehensive Docs</h3>
                        <p>Detailed API documentation with interactive Swagger UI</p>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3>High Performance</h3>
                        <p>Optimized for speed and efficiency with proper caching</p>
                    </div>
                </div>

                <div class="text-center" style="margin: 2rem 0; text-align: center;">
                    <a href="{{ path('app.swagger_ui') }}" class="btn btn-large">
                        <i class="fas fa-book"></i> View API Documentation
                    </a>
                </div>
            </div>

            <h2 style="margin: 2rem 0 1rem; color: var(--secondary-color);">API Endpoints</h2>
            <p style="margin-bottom: 2rem;">The API provides the following main endpoints for interacting with the system:</p>

            <div class="endpoints-grid">
                <div class="endpoint-card">
                    <h3><i class="fas fa-lock"></i> Authentication</h3>
                    <p>Secure JWT-based authentication for users and administrators. Includes login, token refresh, and password management.</p>
                    <span class="endpoint-url">/api/v1/auth</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-users"></i> Consultants</h3>
                    <p>Comprehensive consultant profile management with detailed information, skills, and availability tracking.</p>
                    <span class="endpoint-url">/api/v1/consultants</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-clock"></i> Work Time</h3>
                    <p>Detailed work time tracking with project allocation, client assignment, and billable hours management.</p>
                    <span class="endpoint-url">/api/v1/work-time</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-receipt"></i> Expenses</h3>
                    <p>Complete expense management with categories, receipt uploads, approval workflows, and reimbursement tracking.</p>
                    <span class="endpoint-url">/api/v1/expenses</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-calendar-alt"></i> Leaves</h3>
                    <p>Leave request management with different leave types, approval workflows, and calendar integration.</p>
                    <span class="endpoint-url">/api/v1/leaves</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-building"></i> Company</h3>
                    <p>Company information management including departments, teams, office locations, and organizational structure.</p>
                    <span class="endpoint-url">/api/v1/company</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-chart-line"></i> Dashboard</h3>
                    <p>Comprehensive analytics and reporting endpoints for real-time business intelligence and decision support.</p>
                    <span class="endpoint-url">/api/v1/dashboard</span>
                </div>

                <div class="endpoint-card">
                    <h3><i class="fas fa-file-invoice"></i> Invoicing</h3>
                    <p>Automated invoice generation based on work time entries, with customizable templates and client-specific rates.</p>
                    <span class="endpoint-url">/api/v1/invoices</span>
                </div>
            </div>

            <div class="cta-section">
                <h2>Ready to Get Started?</h2>
                <p>Explore our comprehensive API documentation to learn more about the available endpoints, request/response formats, and authentication requirements.</p>
                <a href="{{ path('app.swagger_ui') }}" class="btn btn-large">
                    <i class="fas fa-book"></i> View API Documentation
                </a>
            </div>

            <div class="card">
                <h2 class="card-title">Integration Guide</h2>
                <p>Follow these steps to integrate with our API:</p>

                <ol style="margin: 1.5rem 0 1.5rem 2rem;">
                    <li style="margin-bottom: 1rem;">
                        <strong>Authentication</strong>: Obtain a JWT token by sending a POST request to <code>/api/v1/auth/login</code> with valid credentials.
                    </li>
                    <li style="margin-bottom: 1rem;">
                        <strong>Authorization</strong>: Include the JWT token in the Authorization header of all subsequent requests using the Bearer scheme.
                    </li>
                    <li style="margin-bottom: 1rem;">
                        <strong>API Requests</strong>: Make requests to the appropriate endpoints based on your requirements.
                    </li>
                    <li style="margin-bottom: 1rem;">
                        <strong>Error Handling</strong>: Handle error responses appropriately. All errors follow a consistent format with meaningful messages.
                    </li>
                </ol>

                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 6px; margin-top: 1.5rem;">
                    <h3 style="margin-bottom: 1rem; color: var(--secondary-color);">Example Authentication Request</h3>
                    <pre style="background: #2c3e50; color: #fff; padding: 1rem; border-radius: 4px; overflow-x: auto;"><code>POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}</code></pre>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; {{ "now"|date("Y") }} FSLI Group. All rights reserved.</p>
            <p style="margin-top: 0.5rem;">
                <a href="{{ path('app.swagger_ui') }}">API Documentation</a> |
                <a href="#">Terms of Service</a> |
                <a href="#">Privacy Policy</a>
            </p>
        </div>
    </footer>
{% endblock %}
