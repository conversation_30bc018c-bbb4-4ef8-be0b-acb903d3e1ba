#!/bin/bash

# Create JWT directory if it doesn't exist
mkdir -p /var/www/html/config/jwt

# Generate the private key
openssl genpkey -out /var/www/html/config/jwt/private.pem -aes256 -algorithm rsa -pkeyopt rsa_keygen_bits:4096 -pass pass:a3b0fd3e7044f017ac736a3b158366bcfa7ba8722da958d6297824f425f61497

# Generate the public key
openssl pkey -in /var/www/html/config/jwt/private.pem -passin pass:a3b0fd3e7044f017ac736a3b158366bcfa7ba8722da958d6297824f425f61497 -out /var/www/html/config/jwt/public.pem -pubout

# Set proper permissions
chmod 644 /var/www/html/config/jwt/private.pem
chmod 644 /var/www/html/config/jwt/public.pem
chown www-data:www-data /var/www/html/config/jwt/private.pem
chown www-data:www-data /var/www/html/config/jwt/public.pem

echo "JWT keys generated successfully!"
