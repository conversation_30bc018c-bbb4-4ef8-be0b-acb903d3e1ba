<?php
// This script creates an admin user directly using Doctrine

require dirname(__DIR__).'/vendor/autoload.php';

use App\Entity\Consultant;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

$kernel = new \App\Kernel($_SERVER['APP_ENV'] ?? 'dev', (bool) ($_SERVER['APP_DEBUG'] ?? true));
$kernel->boot();
$container = $kernel->getContainer();

$entityManager = $container->get(EntityManagerInterface::class);
$passwordHasher = $container->get(UserPasswordHasherInterface::class);

// Check if admin already exists
$repository = $entityManager->getRepository(Consultant::class);
$existingAdmin = $repository->findOneBy(['email' => '<EMAIL>']);

if ($existingAdmin) {
    echo "Admin user already exists!\n";
    exit(0);
}

// Create new admin
$admin = new Consultant();
$admin->setEmail('<EMAIL>');
$admin->setFirstName('Admin');
$admin->setLastName('Fsli');
$admin->setIsAdmin(true);
$admin->setRoles(['ROLE_USER', 'ROLE_ADMIN']);

$hashedPassword = $passwordHasher->hashPassword($admin, 'Password123');
$admin->setPassword($hashedPassword);

$entityManager->persist($admin);
$entityManager->flush();

echo "Admin user created successfully!\n";
