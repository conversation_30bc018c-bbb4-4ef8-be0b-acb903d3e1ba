<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528153450 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update leave system: add leave categories, change default status to approved, add holiday payment setting';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE leave_category (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(50) NOT NULL, description LONGTEXT DEFAULT NULL, requires_description TINYINT(1) NOT NULL, is_active TINYINT(1) NOT NULL, is_paid TINYINT(1) NOT NULL, sort_order INT NOT NULL, UNIQUE INDEX UNIQ_3864316277153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE company ADD holiday_payment_enabled TINYINT(1) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE leave_request ADD category_id INT NOT NULL, DROP type, CHANGE comment description LONGTEXT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE leave_request ADD CONSTRAINT FK_7DC8F77812469DE2 FOREIGN KEY (category_id) REFERENCES leave_category (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7DC8F77812469DE2 ON leave_request (category_id)
        SQL);

        // Change default status from 'pending' to 'approved'
        $this->addSql(<<<'SQL'
            ALTER TABLE leave_request ALTER COLUMN status SET DEFAULT 'approved'
        SQL);

        // Update existing pending leaves to approved (auto-approval)
        $this->addSql(<<<'SQL'
            UPDATE leave_request SET status = 'approved' WHERE status = 'pending'
        SQL);

        // Insert default leave categories
        $this->addSql(<<<'SQL'
            INSERT INTO leave_category (name, code, description, requires_description, is_active, is_paid, sort_order) VALUES
            ('Congés annuels', 'ANNUAL', 'Congés payés annuels', 0, 1, 1, 1),
            ('Maladie', 'SICK', 'Arrêt maladie', 0, 1, 1, 2),
            ('Autre', 'OTHER', 'Autres types de congés', 1, 1, 0, 3)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

        // Restore default status to 'pending'
        $this->addSql(<<<'SQL'
            ALTER TABLE leave_request ALTER COLUMN status SET DEFAULT 'pending'
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE `leave_request` DROP FOREIGN KEY FK_7DC8F77812469DE2
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_7DC8F77812469DE2 ON `leave_request`
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave_request` ADD type VARCHAR(255) NOT NULL, DROP category_id, CHANGE description comment LONGTEXT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE leave_category
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE company DROP holiday_payment_enabled
        SQL);
    }
}
