<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250527121553 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE client (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(50) NOT NULL, is_active TINYINT(1) NOT NULL, address VARCHAR(255) DEFAULT NULL, phone VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, website VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_C744045577153098 (code), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE expense_category (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(50) NOT NULL, description LONGTEXT DEFAULT NULL, requires_receipt TINYINT(1) NOT NULL, is_active TINYINT(1) NOT NULL, parent_category_id INT DEFAULT NULL, UNIQUE INDEX UNIQ_C02DDB3877153098 (code), INDEX IDX_C02DDB38796A8F92 (parent_category_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE trip (id INT AUTO_INCREMENT NOT NULL, title VARCHAR(255) NOT NULL, destination VARCHAR(255) DEFAULT NULL, purpose LONGTEXT DEFAULT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, notes LONGTEXT DEFAULT NULL, consultant_id INT NOT NULL, client_id INT DEFAULT NULL, INDEX IDX_7656F53B44F779A2 (consultant_id), INDEX IDX_7656F53B19EB6921 (client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_category ADD CONSTRAINT FK_C02DDB38796A8F92 FOREIGN KEY (parent_category_id) REFERENCES expense_category (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE trip ADD CONSTRAINT FK_7656F53B44F779A2 FOREIGN KEY (consultant_id) REFERENCES consultant (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE trip ADD CONSTRAINT FK_7656F53B19EB6921 FOREIGN KEY (client_id) REFERENCES client (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense ADD client_id INT DEFAULT NULL, ADD trip_id INT DEFAULT NULL, ADD category_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense ADD CONSTRAINT FK_2D3A8DA619EB6921 FOREIGN KEY (client_id) REFERENCES client (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense ADD CONSTRAINT FK_2D3A8DA6A5BC2E0E FOREIGN KEY (trip_id) REFERENCES trip (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense ADD CONSTRAINT FK_2D3A8DA612469DE2 FOREIGN KEY (category_id) REFERENCES expense_category (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2D3A8DA619EB6921 ON expense (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2D3A8DA6A5BC2E0E ON expense (trip_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2D3A8DA612469DE2 ON expense (category_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_category DROP FOREIGN KEY FK_C02DDB38796A8F92
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE trip DROP FOREIGN KEY FK_7656F53B44F779A2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE trip DROP FOREIGN KEY FK_7656F53B19EB6921
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE client
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE expense_category
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE trip
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense DROP FOREIGN KEY FK_2D3A8DA619EB6921
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense DROP FOREIGN KEY FK_2D3A8DA6A5BC2E0E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense DROP FOREIGN KEY FK_2D3A8DA612469DE2
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2D3A8DA619EB6921 ON expense
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2D3A8DA6A5BC2E0E ON expense
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_2D3A8DA612469DE2 ON expense
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense DROP client_id, DROP trip_id, DROP category_id
        SQL);
    }
}
