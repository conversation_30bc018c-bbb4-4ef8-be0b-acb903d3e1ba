<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250425110235 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE activity (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE company (id INT AUTO_INCREMENT NOT NULL, name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL, address VARCHAR(255) DEFAULT NULL, phone VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, website VARCHAR(255) DEFAULT NULL, siret VARCHAR(255) DEFAULT NULL, annual_leave_days INT DEFAULT NULL, work_hours_per_day DOUBLE PRECISION DEFAULT NULL, max_remote_work_percentage DOUBLE PRECISION DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE consultant (last_name VARCHAR(255) NOT NULL, first_name VARCHAR(255) NOT NULL, phone VARCHAR(255) DEFAULT NULL, is_admin TINYINT(1) NOT NULL, id INT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE expense (id INT AUTO_INCREMENT NOT NULL, date DATE NOT NULL, description VARCHAR(255) NOT NULL, amount NUMERIC(10, 2) NOT NULL, currency VARCHAR(3) NOT NULL, amount_eur NUMERIC(10, 2) NOT NULL, exchange_rate NUMERIC(10, 4) DEFAULT NULL, receipt VARCHAR(255) DEFAULT NULL, validated TINYINT(1) NOT NULL, consultant_id INT NOT NULL, INDEX IDX_2D3A8DA644F779A2 (consultant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE `leave` (id INT AUTO_INCREMENT NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, number_of_days DOUBLE PRECISION NOT NULL, type VARCHAR(255) NOT NULL, comment LONGTEXT DEFAULT NULL, status VARCHAR(20) NOT NULL, consultant_id INT NOT NULL, INDEX IDX_9BB080D044F779A2 (consultant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE user (id INT AUTO_INCREMENT NOT NULL, email VARCHAR(180) NOT NULL, roles JSON NOT NULL, password VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_IDENTIFIER_EMAIL (email), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE work_time (id INT AUTO_INCREMENT NOT NULL, date DATE NOT NULL, hours DOUBLE PRECISION NOT NULL, remote_work TINYINT(1) NOT NULL, activity VARCHAR(255) DEFAULT NULL, comment LONGTEXT DEFAULT NULL, consultant_id INT NOT NULL, INDEX IDX_9657297D44F779A2 (consultant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE consultant ADD CONSTRAINT FK_441282A1BF396750 FOREIGN KEY (id) REFERENCES user (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense ADD CONSTRAINT FK_2D3A8DA644F779A2 FOREIGN KEY (consultant_id) REFERENCES consultant (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave` ADD CONSTRAINT FK_9BB080D044F779A2 FOREIGN KEY (consultant_id) REFERENCES consultant (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time ADD CONSTRAINT FK_9657297D44F779A2 FOREIGN KEY (consultant_id) REFERENCES consultant (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE consultant DROP FOREIGN KEY FK_441282A1BF396750
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense DROP FOREIGN KEY FK_2D3A8DA644F779A2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave` DROP FOREIGN KEY FK_9BB080D044F779A2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time DROP FOREIGN KEY FK_9657297D44F779A2
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE activity
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE company
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE consultant
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE expense
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE `leave`
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE user
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE work_time
        SQL);
    }
}
