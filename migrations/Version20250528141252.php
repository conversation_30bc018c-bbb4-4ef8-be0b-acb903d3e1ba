<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528141252 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE work_time_activity (id INT AUTO_INCREMENT NOT NULL, activity_name VARCHAR(255) NOT NULL, hours DOUBLE PRECISION NOT NULL, description LONGTEXT DEFAULT NULL, is_billable TINYINT(1) NOT NULL, work_time_id INT NOT NULL, client_id INT DEFAULT NULL, INDEX IDX_482C33A28B216519 (work_time_id), INDEX IDX_482C33A219EB6921 (client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity ADD CONSTRAINT FK_482C33A28B216519 FOREIGN KEY (work_time_id) REFERENCES work_time (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity ADD CONSTRAINT FK_482C33A219EB6921 FOREIGN KEY (client_id) REFERENCES client (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE company ADD default_remote_work_percentage DOUBLE PRECISION DEFAULT NULL, ADD work_time_validation_required TINYINT(1) NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time ADD end_date DATE NOT NULL, ADD remote_work_percentage DOUBLE PRECISION NOT NULL, ADD validated_at DATETIME DEFAULT NULL, ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME NOT NULL, ADD validated_by_id INT DEFAULT NULL, DROP activity, CHANGE date start_date DATE NOT NULL, CHANGE hours total_hours DOUBLE PRECISION NOT NULL, CHANGE remote_work is_validated TINYINT(1) NOT NULL, CHANGE comment notes LONGTEXT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time ADD CONSTRAINT FK_9657297DC69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES consultant (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9657297DC69DE5E5 ON work_time (validated_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity DROP FOREIGN KEY FK_482C33A28B216519
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity DROP FOREIGN KEY FK_482C33A219EB6921
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE work_time_activity
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time DROP FOREIGN KEY FK_9657297DC69DE5E5
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_9657297DC69DE5E5 ON work_time
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time ADD date DATE NOT NULL, ADD hours DOUBLE PRECISION NOT NULL, ADD activity VARCHAR(255) DEFAULT NULL, DROP start_date, DROP end_date, DROP total_hours, DROP remote_work_percentage, DROP validated_at, DROP created_at, DROP updated_at, DROP validated_by_id, CHANGE is_validated remote_work TINYINT(1) NOT NULL, CHANGE notes comment LONGTEXT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE company DROP default_remote_work_percentage, DROP work_time_validation_required
        SQL);
    }
}
