<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250503074814 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE `leave_request` (id INT AUTO_INCREMENT NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, number_of_days DOUBLE PRECISION NOT NULL, type VARCHAR(255) NOT NULL, comment LONGTEXT DEFAULT NULL, status VARCHAR(20) NOT NULL, consultant_id INT NOT NULL, INDEX IDX_7DC8F77844F779A2 (consultant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci`
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave_request` ADD CONSTRAINT FK_7DC8F77844F779A2 FOREIGN KEY (consultant_id) REFERENCES consultant (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave` DROP FOREIGN KEY FK_9BB080D044F779A2
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE `leave`
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE `leave` (id INT AUTO_INCREMENT NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, number_of_days DOUBLE PRECISION NOT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, comment LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, status VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, consultant_id INT NOT NULL, INDEX IDX_9BB080D044F779A2 (consultant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = '' 
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave` ADD CONSTRAINT FK_9BB080D044F779A2 FOREIGN KEY (consultant_id) REFERENCES consultant (id) ON UPDATE NO ACTION ON DELETE NO ACTION
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE `leave_request` DROP FOREIGN KEY FK_7DC8F77844F779A2
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE `leave_request`
        SQL);
    }
}
