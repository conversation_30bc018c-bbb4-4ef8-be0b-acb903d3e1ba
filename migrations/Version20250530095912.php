<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530095912 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Move client relationship from WorkTimeActivity to WorkTime';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time ADD client_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time ADD CONSTRAINT FK_9657297D19EB6921 FOREIGN KEY (client_id) REFERENCES client (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9657297D19EB6921 ON work_time (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity DROP FOREIGN KEY FK_482C33A219EB6921
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_482C33A219EB6921 ON work_time_activity
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity DROP client_id
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time DROP FOREIGN KEY FK_9657297D19EB6921
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_9657297D19EB6921 ON work_time
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time DROP client_id
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity ADD client_id INT DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE work_time_activity ADD CONSTRAINT FK_482C33A219EB6921 FOREIGN KEY (client_id) REFERENCES client (id) ON UPDATE NO ACTION ON DELETE NO ACTION
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_482C33A219EB6921 ON work_time_activity (client_id)
        SQL);
    }
}
