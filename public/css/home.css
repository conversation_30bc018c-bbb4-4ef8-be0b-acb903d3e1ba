:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --gray-light: #f1f1f1;
    --gray: #ddd;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    --info: #3498db;
    --border-radius: 6px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
    font-size: 16px;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 2rem 0;
    box-shadow: var(--box-shadow);
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.header-content p {
    font-size: 1.2rem;
    max-width: 800px;
    opacity: 0.9;
}

.main-content {
    padding: 3rem 0;
}

.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray);
    font-size: 1.5rem;
}

.btn {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 0.8rem 1.5rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 600;
    border: none;
    cursor: pointer;
    text-align: center;
}

.btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-secondary {
    background: var(--secondary-color);
}

.btn-secondary:hover {
    background: #1a252f;
}

.endpoints-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.endpoint-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    transition: var(--transition);
    border-top: 4px solid var(--primary-color);
}

.endpoint-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.endpoint-card h3 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.endpoint-card p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.endpoint-url {
    display: block;
    background: var(--gray-light);
    padding: 0.5rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9rem;
    color: var(--secondary-color);
    word-break: break-all;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.feature {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-align: center;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.feature h3 {
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
}

.feature p {
    color: #666;
    font-size: 0.95rem;
}

.cta-section {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: var(--white);
    padding: 3rem 0;
    text-align: center;
    margin: 3rem 0;
    border-radius: var(--border-radius);
}

.cta-section h2 {
    margin-bottom: 1rem;
    font-size: 2rem;
}

.cta-section p {
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.footer {
    background: var(--secondary-color);
    color: var(--white);
    padding: 2rem 0;
    text-align: center;
}

.footer p {
    opacity: 0.8;
}

.footer a {
    color: var(--white);
    text-decoration: none;
    border-bottom: 1px dotted var(--white);
}

.footer a:hover {
    border-bottom: 1px solid var(--white);
}

@media (max-width: 768px) {
    .endpoints-grid {
        grid-template-columns: 1fr;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .header-content p {
        font-size: 1rem;
    }
}
