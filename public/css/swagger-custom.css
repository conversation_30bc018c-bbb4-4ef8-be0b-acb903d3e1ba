:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --gray-light: #f1f1f1;
    --gray: #ddd;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    --info: #3498db;
    --border-radius: 6px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Header styling - hide default topbar */
.swagger-ui .topbar {
    display: none !important;
    height: 0 !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    z-index: -1 !important;
}

/* Info section styling - hide the redundant section */
.swagger-ui .info {
    display: none !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

.swagger-ui .info .title {
    color: var(--secondary-color);
    font-size: 36px;
}

.swagger-ui .info .title small.version-stamp {
    background-color: var(--primary-color);
}

.swagger-ui .info .base-url {
    color: var(--primary-dark);
    font-weight: 600;
}

.swagger-ui .info a {
    color: var(--primary-color);
}

.swagger-ui .info a:hover {
    color: var(--primary-dark);
}

/* Operations styling */
.swagger-ui .opblock.opblock-get {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--primary-color);
}

.swagger-ui .opblock.opblock-get .opblock-summary-method {
    background: var(--primary-color);
}

.swagger-ui .opblock.opblock-post {
    background: rgba(46, 204, 113, 0.1);
    border-color: var(--success);
}

.swagger-ui .opblock.opblock-post .opblock-summary-method {
    background: var(--success);
}

.swagger-ui .opblock.opblock-put {
    background: rgba(243, 156, 18, 0.1);
    border-color: var(--warning);
}

.swagger-ui .opblock.opblock-put .opblock-summary-method {
    background: var(--warning);
}

.swagger-ui .opblock.opblock-delete {
    background: rgba(231, 76, 60, 0.1);
    border-color: var(--danger);
}

.swagger-ui .opblock.opblock-delete .opblock-summary-method {
    background: var(--danger);
}

.swagger-ui .opblock .opblock-summary {
    border-radius: var(--border-radius);
}

.swagger-ui .opblock .opblock-summary-method {
    border-radius: var(--border-radius);
    font-weight: 600;
}

.swagger-ui .opblock .opblock-summary-description {
    font-weight: 500;
    color: var(--text-color);
}

/* Models styling */
.swagger-ui .model-box {
    background: var(--light-bg);
    border-radius: var(--border-radius);
}

.swagger-ui section.models {
    border-color: var(--gray);
    border-radius: var(--border-radius);
}

.swagger-ui section.models.is-open h4 {
    border-bottom-color: var(--gray);
}

.swagger-ui section.models h4 {
    color: var(--secondary-color);
}

/* Buttons styling */
.swagger-ui .btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.swagger-ui .btn.authorize {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.swagger-ui .btn.authorize:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.swagger-ui .btn.execute {
    background-color: var(--success);
    border-color: var(--success);
}

.swagger-ui .btn.execute:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

/* Schema styling */
.swagger-ui .model-title {
    color: var(--secondary-color);
}

.swagger-ui .parameter__name {
    color: var(--secondary-color);
    font-weight: 600;
}

.swagger-ui .parameter__in {
    color: var(--primary-color);
    font-weight: 500;
}

/* Response styling */
.swagger-ui table thead tr th {
    color: var(--secondary-color);
    border-bottom: 2px solid var(--gray);
}

.swagger-ui .responses-table .response-col_status {
    color: var(--primary-color);
    font-weight: 600;
}

/* Tags styling */
.swagger-ui .opblock-tag {
    color: var(--secondary-color);
    font-size: 24px;
    border-bottom: 1px solid var(--gray);
    margin: 20px 0 10px 0;
}

.swagger-ui .opblock-tag:hover {
    background-color: var(--light-bg);
    color: var(--primary-color);
}

/* Authorize button */
.swagger-ui .auth-wrapper .authorize {
    color: var(--primary-color);
}

.swagger-ui .auth-container {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Ensure scheme container is visible with minimal spacing */
.swagger-ui .scheme-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 0 !important;
    padding: 5px 20px !important;
    box-shadow: none !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #eee !important;
    position: static !important;
    z-index: 1 !important;
}

/* Custom header */
.custom-swagger-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 12px 0;
    text-align: left;
    box-shadow: var(--box-shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 10000;
    overflow-y: auto;
    max-height: 100vh;
}

/* Header top section with title and version */
.header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.custom-swagger-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
}

.version-badges {
    display: flex;
    align-items: center;
}

.version-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 0 8px;
    display: inline-block;
}

.header-description {
    margin: 5px 0 10px;
    opacity: 0.9;
    font-size: 15px;
    font-weight: 300;
}

/* Header info sections */
.header-info {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.header-info-section {
    flex: 1;
    min-width: 250px;
    padding: 0 15px;
    margin-bottom: 8px;
}

.header-info-section h3 {
    font-size: 16px;
    margin: 0 0 8px;
    font-weight: 500;
}

.header-info-section p {
    margin: 0;
    line-height: 1.4;
    opacity: 0.9;
    font-size: 14px;
}

.header-links {
    display: flex;
    flex-wrap: wrap;
    margin: -3px;
}

.custom-swagger-header a {
    color: var(--white);
    text-decoration: none;
    margin: 3px;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: inline-block;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.1);
    font-size: 13px;
}

.custom-swagger-header a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.base-url code,
.api-spec code {
    background: rgba(0, 0, 0, 0.2);
    padding: 5px 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    display: inline-block;
    margin-bottom: 5px;
}

/* Custom footer */
.custom-swagger-footer {
    background: var(--secondary-color);
    color: var(--white);
    padding: 15px 0;
    text-align: center;
    margin-top: 50px;
}

.custom-swagger-footer p {
    margin: 0;
    opacity: 0.8;
}

.custom-swagger-footer a {
    color: var(--white);
    text-decoration: none;
    border-bottom: 1px dotted var(--white);
    margin: 0 10px;
}

.custom-swagger-footer a:hover {
    border-bottom: 1px solid var(--white);
}

/* Main content padding for fixed header - applied on page load */
body.custom-swagger-theme {
    padding-top: 190px; /* Further reduced padding for tighter layout */
}

.swagger-ui {
    position: relative; /* Ensure proper stacking context */
}

/* Ensure the scheme container doesn't overlap with content */
.swagger-ui .scheme-container {
    margin-top: 0 !important;
    position: static !important;
}

/* Remove extra spacing between scheme container and endpoints */
.swagger-ui .wrapper {
    padding: 0 !important;
    margin: 0 !important;
}

/* Adjust spacing for the operations container */
.swagger-ui .opblock-tag-section {
    margin: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .swagger-ui .info .title {
        font-size: 28px;
    }

    .custom-swagger-header h1 {
        font-size: 24px;
    }

    .swagger-ui {
        padding-top: 0; /* No padding on mobile */
    }

    .header-info {
        flex-direction: column;
    }
}
