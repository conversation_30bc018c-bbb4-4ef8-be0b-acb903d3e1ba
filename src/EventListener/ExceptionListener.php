<?php

namespace App\EventListener;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ExceptionListener
{
    private bool $debug;

    public function __construct(string $environment = 'prod')
    {
        $this->debug = $environment === 'dev';
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();
        $response = new JsonResponse();

        if ($exception instanceof HttpException) {
            $data = [
                'message' => $exception->getMessage(),
                'code' => $exception->getStatusCode()
            ];
            $statusCode = $exception->getStatusCode();
        } else {
            $data = [
                'message' => 'An internal server error occurred',
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
            $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
        }

        // Add detailed error information in debug mode
        if ($this->debug) {
            $data['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        $response->setData($data);
        $response->setStatusCode($statusCode);
        $event->setResponse($response);
    }
}