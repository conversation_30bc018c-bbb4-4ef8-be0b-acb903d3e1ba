<?php

namespace App\Controller;

use App\Service\DashboardService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use App\Entity\Consultant;
use OpenApi\Attributes as OA;

/**
 * Controller for dashboard data and analytics
 */
#[Route('/api/v1/dashboard')]
#[OA\Tag(name: 'Dashboard')]
class DashboardController extends AbstractController
{
    public function __construct(
        private readonly DashboardService $dashboardService
    ) {}

    /**
     * Get dashboard data for a specific consultant
     */
    #[Route('/consultant/{consultantId}', name: 'app_dashboard_consultant', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'The consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'debut',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'fin',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns dashboard data for the consultant',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>')
                    ]
                ),
                new OA\Property(
                    property: 'period',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'start', type: 'string', format: 'date', example: '2023-06-01'),
                        new OA\Property(property: 'end', type: 'string', format: 'date', example: '2023-06-30')
                    ]
                ),
                new OA\Property(
                    property: 'workTime',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'totalHours', type: 'number', format: 'float', example: 160.0),
                        new OA\Property(property: 'remoteHours', type: 'number', format: 'float', example: 80.0),
                        new OA\Property(property: 'remoteWorkPercentage', type: 'number', format: 'float', example: 50.0)
                    ]
                ),
                new OA\Property(
                    property: 'expenses',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'totalEur', type: 'number', format: 'float', example: 450.75)
                    ]
                ),
                new OA\Property(
                    property: 'leaves',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'remainingDays', type: 'number', format: 'float', example: 15.5),
                        new OA\Property(property: 'year', type: 'integer', example: 2023)
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Invalid date format'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'You can only access your own dashboard'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Consultant not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function getConsultantDashboard(int $consultantId, Request $request): JsonResponse
    {
        try {
            // Check if current user is admin or the consultant themselves
            if (!$this->isGranted('ROLE_ADMIN') && $this->getUser()->getId() !== $consultantId) {
                return $this->json([
                    'message' => 'You can only access your own dashboard',
                    'success' => false
                ], 403);
            }

            if (!$request->query->has('debut') || !$request->query->has('fin')) {
                // Default to current month if no dates provided
                $debut = new \DateTime('first day of this month');
                $fin = new \DateTime('last day of this month');
            } else {
                $debut = new \DateTime($request->query->get('debut'));
                $fin = new \DateTime($request->query->get('fin'));
            }

            $dashboard = $this->dashboardService->getConsultantDashboard($consultantId, $debut, $fin);

            return $this->json($dashboard);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'message' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (\Exception $e) {
            return $this->json([
                'message' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get admin dashboard data (admin only)
     */
    #[Route('/admin', name: 'app_dashboard_admin', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'debut',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'fin',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns admin dashboard data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'period',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'start', type: 'string', format: 'date', example: '2023-06-01'),
                        new OA\Property(property: 'end', type: 'string', format: 'date', example: '2023-06-30')
                    ]
                ),
                new OA\Property(
                    property: 'global',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'consultantCount', type: 'integer', example: 10),
                        new OA\Property(property: 'totalHours', type: 'number', format: 'float', example: 1600.0),
                        new OA\Property(property: 'remoteHours', type: 'number', format: 'float', example: 800.0),
                        new OA\Property(property: 'remoteWorkPercentage', type: 'number', format: 'float', example: 50.0),
                        new OA\Property(property: 'totalExpenses', type: 'number', format: 'float', example: 4500.75)
                    ]
                ),
                new OA\Property(
                    property: 'consultants',
                    type: 'array',
                    items: new OA\Items(
                        properties: [
                            new OA\Property(
                                property: 'consultant',
                                type: 'object',
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                                    new OA\Property(property: 'firstName', type: 'string', example: 'John')
                                ]
                            ),
                            new OA\Property(property: 'totalHours', type: 'number', format: 'float', example: 160.0),
                            new OA\Property(property: 'remoteHours', type: 'number', format: 'float', example: 80.0),
                            new OA\Property(property: 'remoteWorkPercentage', type: 'number', format: 'float', example: 50.0),
                            new OA\Property(property: 'totalExpenses', type: 'number', format: 'float', example: 450.75)
                        ]
                    )
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Invalid date format'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Admin access required'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function getAdminDashboard(Request $request): JsonResponse
    {
        try {
            if (!$request->query->has('debut') || !$request->query->has('fin')) {
                // Default to current month if no dates provided
                $debut = new \DateTime('first day of this month');
                $fin = new \DateTime('last day of this month');
            } else {
                $debut = new \DateTime($request->query->get('debut'));
                $fin = new \DateTime($request->query->get('fin'));
            }

            $dashboard = $this->dashboardService->getAdminDashboard($debut, $fin);

            return $this->json($dashboard);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'message' => $e->getMessage(),
                'success' => false
            ], 403);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'message' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (\Exception $e) {
            return $this->json([
                'message' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
