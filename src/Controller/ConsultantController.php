<?php

namespace App\Controller;

use App\Entity\Consultant;
use App\Service\ConsultantService;
use OpenApi\Attributes as OA;
use App\Controller\AbstractApiController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Controller for managing consultants
 */
#[Route('/api/v1/consultants')]
#[OA\Tag(name: 'Consultants')]
class ConsultantController extends AbstractApiController
{
    public function __construct(
        private readonly ConsultantService $consultantService
    ) {}

    /**
     * Get all consultants (admin only)
     */
    #[Route('', name: 'app_consultant_index', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'Returns the list of all consultants',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(ref: '#/components/schemas/Consultant')
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function index(): JsonResponse
    {
        $consultants = $this->consultantService->getAllConsultants();

        $data = [];
        foreach ($consultants as $consultant) {
            $data[] = $this->serializeConsultant($consultant);
        }

        return $this->json($data);
    }

    /**
     * Get the current consultant's profile
     */
    #[Route('/me', name: 'app_consultant_current', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns the current consultant\'s profile',
        content: new OA\JsonContent(ref: '#/components/schemas/Consultant')
    )]
    #[OA\Response(
        response: 401,
        description: 'User not authenticated'
    )]
    public function currentConsultant(): JsonResponse
    {
        $user = $this->getUser();

        if (!$user) {
            return $this->json(['error' => 'User not authenticated'], Response::HTTP_UNAUTHORIZED);
        }

        if (!$user instanceof Consultant) {
            return $this->json(['error' => 'User is not a consultant'], Response::HTTP_FORBIDDEN);
        }

        return $this->json($this->serializeConsultant($user, false));
    }

    /**
     * Get a specific consultant
     */
    #[Route('/{id}', name: 'app_consultant_show', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        description: 'The consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the consultant details',
        content: new OA\JsonContent(ref: '#/components/schemas/Consultant')
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied'
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found'
    )]
    public function show(int $id): JsonResponse
    {
        try {
            $consultant = $this->consultantService->getConsultantById($id);

            // Check if current user is admin or the consultant themselves
            if (!$this->isGranted('ROLE_ADMIN') &&
                $this->getUser() instanceof Consultant &&
                $this->getUser()->getUserIdentifier() !== $consultant->getEmail()) {
                return $this->json(['error' => 'Access denied'], Response::HTTP_FORBIDDEN);
            }

            return $this->json($this->serializeConsultant($consultant));
        } catch (NotFoundHttpException $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Create a new consultant (admin only)
     */
    #[Route('', name: 'app_consultant_create', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Consultant data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'password', type: 'string', format: 'password', example: 'securePassword123'),
                new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                new OA\Property(property: 'phone', type: 'string', example: '+33123456789'),
                new OA\Property(property: 'isAdmin', type: 'boolean', example: false)
            ],
            required: ['email', 'password', 'firstName', 'lastName']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Consultant created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Consultant created successfully'),
                new OA\Property(property: 'id', type: 'integer', example: 1)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data'
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function create(Request $request): JsonResponse
    {
        $data = $this->getJsonData($request);

        // Validate required fields
        $requiredFields = ['email', 'password', 'lastName', 'firstName'];
        $missingFields = $this->validateRequiredFields($data, $requiredFields);

        if (!empty($missingFields)) {
            return $this->json([
                'error' => 'Missing required fields: ' . implode(', ', $missingFields)
            ], Response::HTTP_BAD_REQUEST);
        }

        try {
            $consultant = $this->consultantService->createConsultant($data);

            return $this->json([
                'message' => 'Consultant created successfully',
                'id' => $consultant->getId()
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Update an existing consultant
     */
    #[Route('/{id}', name: 'app_consultant_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        description: 'The consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated consultant data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>'),
                new OA\Property(property: 'password', type: 'string', format: 'password', example: 'newPassword123'),
                new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
                new OA\Property(property: 'phone', type: 'string', example: '+33123456789'),
                new OA\Property(property: 'isAdmin', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Consultant updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Consultant updated successfully'),
                new OA\Property(property: 'id', type: 'integer', example: 1)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data'
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied'
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found'
    )]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $consultant = $this->consultantService->getConsultantById($id);

            // Check if current user is admin or the consultant themselves
            if (!$this->isGranted('ROLE_ADMIN') &&
                $this->getUser() instanceof Consultant &&
                $this->getUser()->getUserIdentifier() !== $consultant->getEmail()) {
                return $this->json(['error' => 'Access denied'], Response::HTTP_FORBIDDEN);
            }

            // Only admins can change admin status
            $data = $this->getJsonData($request);
            if (isset($data['isAdmin']) && !$this->isGranted('ROLE_ADMIN')) {
                unset($data['isAdmin']);
            }

            $consultant = $this->consultantService->updateConsultant($id, $data);

            return $this->json([
                'message' => 'Consultant updated successfully',
                'id' => $consultant->getId()
            ]);
        } catch (NotFoundHttpException $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Delete a consultant (admin only)
     */
    #[Route('/{id}', name: 'app_consultant_delete', methods: ['DELETE'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'The consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Consultant deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Consultant deleted successfully')
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found'
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            $this->consultantService->deleteConsultant($id);
            return $this->json(['message' => 'Consultant deleted successfully']);
        } catch (NotFoundHttpException $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Serialize a consultant entity to an array
     *
     * @param Consultant $consultant The consultant to serialize
     * @param bool $includePhone Whether to include the phone number (default: true)
     *
     * @return array<string, mixed> The serialized consultant
     */
    private function serializeConsultant(Consultant $consultant, bool $includePhone = true): array
    {
        $data = [
            'id' => $consultant->getId(),
            'email' => $consultant->getEmail(),
            'lastName' => $consultant->getLastName(),
            'firstName' => $consultant->getFirstName(),
            'isAdmin' => $consultant->isAdmin(),
        ];

        if ($includePhone) {
            $data['phone'] = $consultant->getPhone();
        }

        return $data;
    }

    /**
     * Get JSON data from a request
     *
     * @param Request $request The HTTP request
     *
     * @return array<string, mixed> The decoded JSON data
     */
    private function getJsonData(Request $request): array
    {
        $content = $request->getContent();
        if (empty($content)) {
            return [];
        }

        return json_decode($content, true) ?? [];
    }

    /**
     * Validate that required fields are present in the data
     *
     * @param array<string, mixed> $data The data to validate
     * @param array<string> $requiredFields The required field names
     *
     * @return array<string> Missing field names
     */
    private function validateRequiredFields(array $data, array $requiredFields): array
    {
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $missingFields[] = $field;
            }
        }

        return $missingFields;
    }
}
