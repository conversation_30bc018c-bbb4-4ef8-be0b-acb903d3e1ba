<?php

namespace App\Controller;

use App\Service\CompanyService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use OpenApi\Attributes as OA;

/**
 * Controller for managing company information
 */
#[Route('/api/v1/company')]
#[OA\Tag(name: 'Company')]
class CompanyController extends AbstractController
{
    public function __construct(
        private readonly CompanyService $companyService
    ) {}

    /**
     * Get company information
     */
    #[Route('', name: 'app_company_show', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns company information',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                new OA\Property(property: 'address', type: 'string', nullable: true, example: '123 Main St, City'),
                new OA\Property(property: 'phone', type: 'string', nullable: true, example: '+33123456789'),
                new OA\Property(property: 'email', type: 'string', nullable: true, example: '<EMAIL>'),
                new OA\Property(property: 'website', type: 'string', nullable: true, example: 'https://acme.com'),
                new OA\Property(property: 'siret', type: 'string', nullable: true, example: '12345678901234'),
                new OA\Property(property: 'annualLeaveDays', type: 'integer', example: 25),
                new OA\Property(property: 'workHoursPerDay', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'maxRemoteWorkPercentage', type: 'number', format: 'float', example: 50.0)
            ]
        )
    )]
    public function show(): JsonResponse
    {
        try {
            $company = $this->companyService->getCompanyInfo();

            // Format the response
            $response = [
                'id' => $company->getId(),
                'name' => $company->getName(),
                'address' => $company->getAddress(),
                'phone' => $company->getPhone(),
                'email' => $company->getEmail(),
                'website' => $company->getWebsite(),
                'siret' => $company->getSiret(),
                'annualLeaveDays' => $company->getAnnualLeaveDays(),
                'workHoursPerDay' => $company->getWorkHoursPerDay(),
                'maxRemoteWorkPercentage' => $company->getMaxRemoteWorkPercentage()
            ];

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Update company information (admin only)
     */
    #[Route('', name: 'app_company_update', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Company information to update',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                new OA\Property(property: 'address', type: 'string', nullable: true, example: '123 Main St, City'),
                new OA\Property(property: 'phone', type: 'string', nullable: true, example: '+33123456789'),
                new OA\Property(property: 'email', type: 'string', nullable: true, example: '<EMAIL>'),
                new OA\Property(property: 'website', type: 'string', nullable: true, example: 'https://acme.com'),
                new OA\Property(property: 'siret', type: 'string', nullable: true, example: '12345678901234'),
                new OA\Property(property: 'annualLeaveDays', type: 'integer', example: 25),
                new OA\Property(property: 'workHoursPerDay', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'maxRemoteWorkPercentage', type: 'number', format: 'float', example: 50.0)
            ],
            required: ['name']
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Company information updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                new OA\Property(property: 'address', type: 'string', nullable: true, example: '123 Main St, City'),
                new OA\Property(property: 'phone', type: 'string', nullable: true, example: '+33123456789'),
                new OA\Property(property: 'email', type: 'string', nullable: true, example: '<EMAIL>'),
                new OA\Property(property: 'website', type: 'string', nullable: true, example: 'https://acme.com'),
                new OA\Property(property: 'siret', type: 'string', nullable: true, example: '12345678901234'),
                new OA\Property(property: 'annualLeaveDays', type: 'integer', example: 25),
                new OA\Property(property: 'workHoursPerDay', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'maxRemoteWorkPercentage', type: 'number', format: 'float', example: 50.0)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Company name is required'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Admin access required'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function update(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $company = $this->companyService->updateCompanyInfo($data);

            // Format the response
            $response = [
                'id' => $company->getId(),
                'name' => $company->getName(),
                'address' => $company->getAddress(),
                'phone' => $company->getPhone(),
                'email' => $company->getEmail(),
                'website' => $company->getWebsite(),
                'siret' => $company->getSiret(),
                'annualLeaveDays' => $company->getAnnualLeaveDays(),
                'workHoursPerDay' => $company->getWorkHoursPerDay(),
                'maxRemoteWorkPercentage' => $company->getMaxRemoteWorkPercentage()
            ];

            return $this->json($response);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
