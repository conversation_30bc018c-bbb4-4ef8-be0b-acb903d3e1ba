<?php

namespace App\Controller;

use App\Entity\Client;
use App\Repository\ClientRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use OpenApi\Attributes as OA;

/**
 * Controller for managing clients
 */
#[Route('/api/v1/clients')]
#[OA\Tag(name: 'Clients')]
class ClientController extends AbstractController
{
    public function __construct(
        private readonly ClientRepository $clientRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    /**
     * Get all clients
     */
    #[Route('', name: 'app_client_index', methods: ['GET'])]
    #[OA\Parameter(
        name: 'active',
        description: 'Filter by active status',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of clients',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                    new OA\Property(property: 'code', type: 'string', example: 'ACME'),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true),
                    new OA\Property(property: 'address', type: 'string', nullable: true, example: '123 Main St'),
                    new OA\Property(property: 'phone', type: 'string', nullable: true, example: '+1234567890'),
                    new OA\Property(property: 'email', type: 'string', nullable: true, example: '<EMAIL>'),
                    new OA\Property(property: 'website', type: 'string', nullable: true, example: 'https://acme.com')
                ]
            )
        )
    )]
    public function index(Request $request): JsonResponse
    {
        $activeFilter = $request->query->get('active');
        
        if ($activeFilter !== null) {
            $active = filter_var($activeFilter, FILTER_VALIDATE_BOOLEAN);
            $clients = $active ? $this->clientRepository->findActive() : $this->clientRepository->findAll();
        } else {
            $clients = $this->clientRepository->findAll();
        }

        $response = array_map(fn(Client $client) => [
            'id' => $client->getId(),
            'name' => $client->getName(),
            'code' => $client->getCode(),
            'isActive' => $client->isActive(),
            'address' => $client->getAddress(),
            'phone' => $client->getPhone(),
            'email' => $client->getEmail(),
            'website' => $client->getWebsite()
        ], $clients);

        return $this->json($response);
    }

    /**
     * Get a specific client
     */
    #[Route('/{id}', name: 'app_client_show', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Client details',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                new OA\Property(property: 'code', type: 'string', example: 'ACME'),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'address', type: 'string', nullable: true, example: '123 Main St'),
                new OA\Property(property: 'phone', type: 'string', nullable: true, example: '+1234567890'),
                new OA\Property(property: 'email', type: 'string', nullable: true, example: '<EMAIL>'),
                new OA\Property(property: 'website', type: 'string', nullable: true, example: 'https://acme.com')
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Client not found')]
    public function show(int $id): JsonResponse
    {
        $client = $this->clientRepository->find($id);

        if (!$client) {
            return $this->json([
                'error' => 'Client not found',
                'success' => false
            ], 404);
        }

        $response = [
            'id' => $client->getId(),
            'name' => $client->getName(),
            'code' => $client->getCode(),
            'isActive' => $client->isActive(),
            'address' => $client->getAddress(),
            'phone' => $client->getPhone(),
            'email' => $client->getEmail(),
            'website' => $client->getWebsite()
        ];

        return $this->json($response);
    }

    /**
     * Create a new client (admin only)
     */
    #[Route('', name: 'app_client_create', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Client data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                new OA\Property(property: 'code', type: 'string', example: 'ACME'),
                new OA\Property(property: 'address', type: 'string', nullable: true, example: '123 Main St'),
                new OA\Property(property: 'phone', type: 'string', nullable: true, example: '+1234567890'),
                new OA\Property(property: 'email', type: 'string', nullable: true, example: '<EMAIL>'),
                new OA\Property(property: 'website', type: 'string', nullable: true, example: 'https://acme.com'),
                new OA\Property(property: 'isActive', type: 'boolean', example: true)
            ],
            required: ['name', 'code']
        )
    )]
    #[OA\Response(response: 201, description: 'Client created successfully')]
    #[OA\Response(response: 400, description: 'Invalid data')]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!isset($data['name']) || !isset($data['code'])) {
                return $this->json([
                    'error' => 'Name and code are required',
                    'success' => false
                ], 400);
            }

            // Check if code already exists
            if ($this->clientRepository->findByCode($data['code'])) {
                return $this->json([
                    'error' => 'Client code already exists',
                    'success' => false
                ], 400);
            }

            $client = new Client();
            $client->setName($data['name']);
            $client->setCode($data['code']);
            $client->setAddress($data['address'] ?? null);
            $client->setPhone($data['phone'] ?? null);
            $client->setEmail($data['email'] ?? null);
            $client->setWebsite($data['website'] ?? null);
            $client->setIsActive($data['isActive'] ?? true);

            $this->entityManager->persist($client);
            $this->entityManager->flush();

            return $this->json([
                'id' => $client->getId(),
                'name' => $client->getName(),
                'code' => $client->getCode(),
                'isActive' => $client->isActive(),
                'address' => $client->getAddress(),
                'phone' => $client->getPhone(),
                'email' => $client->getEmail(),
                'website' => $client->getWebsite(),
                'success' => true
            ], 201);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to create client: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
