<?php

namespace App\Controller;

use App\Entity\Expense;
use App\Service\ExpenseService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use OpenApi\Attributes as OA;

/**
 * Controller for managing expenses
 */
#[Route('/api/v1/expenses')]
#[OA\Tag(name: 'Expenses')]
class ExpenseController extends AbstractController
{
    public function __construct(
        private readonly ExpenseService $expenseService
    ) {}

    /**
     * Get all expenses (admin only)
     */
    #[Route('', name: 'app_expense_index', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns all expenses',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                    new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                    new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                    new OA\Property(property: 'amountEur', type: 'string', example: '125.50'),
                    new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                    new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf'),
                    new OA\Property(property: 'validated', type: 'boolean', example: false),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function index(Request $request): JsonResponse
    {
        $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

        $expenses = $this->expenseService->getAllExpenses($startDate, $endDate);

        // Format the response
        $response = array_map(fn(Expense $expense) => [
            'id' => $expense->getId(),
            'date' => $expense->getDate()->format('Y-m-d'),
            'description' => $expense->getDescription(),
            'amount' => $expense->getAmount(),
            'currency' => $expense->getCurrency(),
            'amountEur' => $expense->getAmountEur(),
            'exchangeRate' => $expense->getExchangeRate(),
            'receipt' => $expense->getReceipt(),
            'validated' => $expense->isValidated(),
            'consultant' => [
                'id' => $expense->getConsultant()->getId(),
                'firstName' => $expense->getConsultant()->getFirstName(),
                'lastName' => $expense->getConsultant()->getLastName()
            ],
            'client' => $expense->getClient() ? [
                'id' => $expense->getClient()->getId(),
                'name' => $expense->getClient()->getName(),
                'code' => $expense->getClient()->getCode()
            ] : null,
            'trip' => $expense->getTrip() ? [
                'id' => $expense->getTrip()->getId(),
                'title' => $expense->getTrip()->getTitle(),
                'destination' => $expense->getTrip()->getDestination()
            ] : null,
            'category' => $expense->getCategory() ? [
                'id' => $expense->getCategory()->getId(),
                'name' => $expense->getCategory()->getName(),
                'code' => $expense->getCategory()->getCode()
            ] : null
        ], $expenses);

        return $this->json($response);
    }

    /**
     * Get all non-validated expenses (admin only)
     */
    #[Route('/non-validated', name: 'app_expense_non_validated', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'Returns all non-validated expenses',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                    new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                    new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                    new OA\Property(property: 'amountEur', type: 'string', example: '125.50'),
                    new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                    new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf'),
                    new OA\Property(property: 'validated', type: 'boolean', example: false),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function nonValidated(): JsonResponse
    {
        $expenses = $this->expenseService->getNonValidatedExpenses();

        // Format the response
        $response = array_map(fn(Expense $expense) => [
            'id' => $expense->getId(),
            'date' => $expense->getDate()->format('Y-m-d'),
            'description' => $expense->getDescription(),
            'amount' => $expense->getAmount(),
            'currency' => $expense->getCurrency(),
            'amountEur' => $expense->getAmountEur(),
            'exchangeRate' => $expense->getExchangeRate(),
            'receipt' => $expense->getReceipt(),
            'validated' => $expense->isValidated(),
            'consultant' => [
                'id' => $expense->getConsultant()->getId(),
                'firstName' => $expense->getConsultant()->getFirstName(),
                'lastName' => $expense->getConsultant()->getLastName()
            ]
        ], $expenses);

        return $this->json($response);
    }

    /**
     * Get all expenses for the current user
     *
     * This endpoint allows consultants to view their own expenses without specifying their consultant ID.
     * Supports the same filtering options as the consultant-specific endpoint for detailed expense reporting.
     *
     * ## Travel Expense Filtering Examples:
     * - Get all expenses for a trip: `/my?tripId=1`
     * - Get all flight expenses: `/my?categoryId=5`
     * - Get expenses for a specific client: `/my?clientId=1`
     * - Get expenses for a date range: `/my?start=2023-06-01&end=2023-06-30`
     */
    #[Route('/my', name: 'app_expense_my', methods: ['GET'])]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date filter (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date filter (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'tripId',
        description: 'Filter by trip ID',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'clientId',
        description: 'Filter by client ID',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'categoryId',
        description: 'Filter by expense category ID',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of current user expenses',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'description', type: 'string', example: 'Flight to Paris'),
                    new OA\Property(property: 'amount', type: 'string', example: '450.00'),
                    new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                    new OA\Property(property: 'amountEur', type: 'string', example: '450.00'),
                    new OA\Property(property: 'exchangeRate', type: 'string', nullable: true, example: '1.0000'),
                    new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipt_123.pdf'),
                    new OA\Property(property: 'validated', type: 'boolean', example: false)
                ]
            )
        )
    )]
    #[OA\Response(response: 401, description: 'Authentication required')]
    public function my(Request $request): JsonResponse
    {
        try {
            // Get current user's consultant ID
            /** @var \App\Entity\User $user */
            $user = $this->getUser();
            $consultantId = $user->getId();

            // Parse query parameters
            $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
            $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;
            $tripId = $request->query->get('tripId') ? (int) $request->query->get('tripId') : null;
            $clientId = $request->query->get('clientId') ? (int) $request->query->get('clientId') : null;
            $categoryId = $request->query->get('categoryId') ? (int) $request->query->get('categoryId') : null;

            // Get expenses using the existing service method
            $expenses = $this->expenseService->getExpensesByConsultant(
                $consultantId,
                $startDate,
                $endDate
            );

            // Apply additional filtering if needed
            if ($tripId || $clientId || $categoryId) {
                $expenses = array_filter($expenses, function(Expense $expense) use ($tripId, $clientId, $categoryId) {
                    if ($tripId && (!$expense->getTrip() || $expense->getTrip()->getId() !== $tripId)) {
                        return false;
                    }
                    if ($clientId && (!$expense->getClient() || $expense->getClient()->getId() !== $clientId)) {
                        return false;
                    }
                    if ($categoryId && (!$expense->getCategory() || $expense->getCategory()->getId() !== $categoryId)) {
                        return false;
                    }
                    return true;
                });
            }

            // Format the response
            $response = array_map(fn(Expense $expense) => [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'description' => $expense->getDescription(),
                'categoryId' => $expense->getCategory()->getId(),
                'amount' => $expense->getAmount(),
                'currency' => $expense->getCurrency(),
                'amountEur' => $expense->getAmountEur(),
                'exchangeRate' => $expense->getExchangeRate(),
                'receipt' => $expense->getReceipt(),
                'validated' => $expense->isValidated()
            ], $expenses);

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to retrieve expenses: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get a specific expense by ID
     */
    #[Route('/{id}', name: 'app_expense_show', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Expense ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the expense details',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                new OA\Property(property: 'amountEur', type: 'string', example: '125.50'),
                new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf'),
                new OA\Property(property: 'validated', type: 'boolean', example: false),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Expense not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Expense not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The expense with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function show(int $id): JsonResponse
    {
        try {
            $expense = $this->expenseService->getExpenseById($id);

            // Format the response
            $response = [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'description' => $expense->getDescription(),
                'amount' => $expense->getAmount(),
                'currency' => $expense->getCurrency(),
                'amountEur' => $expense->getAmountEur(),
                'exchangeRate' => $expense->getExchangeRate(),
                'receipt' => $expense->getReceipt(),
                'validated' => $expense->isValidated(),
                'consultant' => [
                    'id' => $expense->getConsultant()->getId(),
                    'firstName' => $expense->getConsultant()->getFirstName(),
                    'lastName' => $expense->getConsultant()->getLastName()
                ]
            ];

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Expense not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Get expenses for a specific consultant
     *
     * This endpoint allows consultants to view their own expenses or administrators to view any consultant's expenses.
     * Supports filtering by date range, trip, client, and category for detailed expense reporting.
     *
     * ## Travel Expense Filtering Examples:
     * - Get all expenses for a trip: `/consultant/1?tripId=1`
     * - Get all flight expenses: `/consultant/1?categoryId=5`
     * - Get expenses for a specific client: `/consultant/1?clientId=1`
     * - Get expenses for a date range: `/consultant/1?start=2023-06-01&end=2023-06-30`
     */
    #[Route('/consultant/{consultantId}', name: 'app_expense_by_consultant', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'Consultant ID - consultants can only access their own expenses, admins can access any',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer'),
        example: 1
    )]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date filter (format: Y-m-d) - useful for monthly/quarterly reports',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date'),
        example: '2023-06-01'
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date filter (format: Y-m-d) - useful for monthly/quarterly reports',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date'),
        example: '2023-06-30'
    )]
    #[OA\Parameter(
        name: 'tripId',
        description: 'Filter by specific trip ID - get all expenses for a particular business trip',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer'),
        example: 1
    )]
    #[OA\Parameter(
        name: 'clientId',
        description: 'Filter by specific client ID - useful for client-specific expense reports',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer'),
        example: 1
    )]
    #[OA\Parameter(
        name: 'categoryId',
        description: 'Filter by expense category - useful for viewing specific types of expenses (e.g., all flights, hotels)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'integer'),
        example: 5
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns expenses for the consultant with full relationship information including trip, client, and category details',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'description', type: 'string', example: 'Round-trip flight Paris-Zurich'),
                    new OA\Property(property: 'amount', type: 'string', example: '450.00'),
                    new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                    new OA\Property(property: 'amountEur', type: 'string', example: '450.00'),
                    new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                    new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/flight-AF1234.pdf'),
                    new OA\Property(property: 'validated', type: 'boolean', example: false),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Smith')
                        ]
                    ),
                    new OA\Property(
                        property: 'client',
                        type: 'object',
                        nullable: true,
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                            new OA\Property(property: 'code', type: 'string', example: 'ACME')
                        ]
                    ),
                    new OA\Property(
                        property: 'trip',
                        type: 'object',
                        nullable: true,
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'title', type: 'string', example: 'Client Meeting in Paris'),
                            new OA\Property(property: 'destination', type: 'string', example: 'Paris, France')
                        ]
                    ),
                    new OA\Property(
                        property: 'category',
                        type: 'object',
                        nullable: true,
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 5),
                            new OA\Property(property: 'name', type: 'string', example: 'Flight Tickets'),
                            new OA\Property(property: 'code', type: 'string', example: 'FLIGHT')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The consultant with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function byConsultant(int $consultantId, Request $request): JsonResponse
    {
        try {
            $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
            $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

            $expenses = $this->expenseService->getExpensesByConsultant($consultantId, $startDate, $endDate);

            // Format the response
            $response = array_map(fn(Expense $expense) => [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'description' => $expense->getDescription(),
                'amount' => $expense->getAmount(),
                'currency' => $expense->getCurrency(),
                'amountEur' => $expense->getAmountEur(),
                'exchangeRate' => $expense->getExchangeRate(),
                'receipt' => $expense->getReceipt(),
                'validated' => $expense->isValidated()
            ], $expenses);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Create a new expense
     *
     * This endpoint allows consultants to record business expenses with comprehensive categorization and trip association.
     *
     * ## Travel Expense Workflow:
     * 1. **Create a Trip** (optional but recommended): Use POST /api/v1/trips to create a trip container
     * 2. **Add Individual Expenses**: Link each expense to the trip using tripId
     * 3. **Automatic Client Assignment**: If trip has a client, it's automatically assigned to the expense
     * 4. **Category Selection**: Choose appropriate expense category for proper reporting
     * 5. **Receipt Management**: Upload receipts for categories that require them
     *
     * ## Example Travel Expenses:
     * - Flight: categoryId=5 (Flight Tickets), requires receipt
     * - Hotel: categoryId=8 (Hotel), requires receipt
     * - Taxi: categoryId=7 (Taxi & Rideshare), requires receipt
     * - Meals: categoryId=12 (Business Meals), requires receipt
     * - Parking: categoryId=9 (Parking), receipt optional
     */
    #[Route('', name: 'app_expense_create', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Expense data for recording business expenses. Can be linked to trips and clients for better organization. When tripId is provided and the trip has a client, the client is automatically assigned to the expense.',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'consultantId',
                    type: 'integer',
                    example: 1,
                    description: 'ID of the consultant recording the expense'
                ),
                new OA\Property(
                    property: 'clientId',
                    type: 'integer',
                    nullable: true,
                    example: 1,
                    description: 'ID of the client (optional, auto-assigned if trip has client)'
                ),
                new OA\Property(
                    property: 'tripId',
                    type: 'integer',
                    nullable: true,
                    example: 1,
                    description: 'ID of the trip to associate this expense with (recommended for travel expenses)'
                ),
                new OA\Property(
                    property: 'categoryId',
                    type: 'integer',
                    nullable: true,
                    example: 5,
                    description: 'ID of the expense category (e.g., 5=Flight, 7=Taxi, 8=Hotel, 12=Business Meals)'
                ),
                new OA\Property(
                    property: 'date',
                    type: 'string',
                    format: 'date',
                    example: '2023-06-15',
                    description: 'Date when the expense occurred (format: Y-m-d)'
                ),
                new OA\Property(
                    property: 'description',
                    type: 'string',
                    example: 'Round-trip flight Paris-Zurich for client meeting',
                    description: 'Detailed description of the expense'
                ),
                new OA\Property(
                    property: 'amount',
                    type: 'string',
                    example: '450.00',
                    description: 'Expense amount in the specified currency'
                ),
                new OA\Property(
                    property: 'currency',
                    type: 'string',
                    example: 'EUR',
                    description: 'Currency code (EUR, USD, CHF, etc.)'
                ),
                new OA\Property(
                    property: 'receipt',
                    type: 'string',
                    nullable: true,
                    example: 'receipts/flight-AF1234.pdf',
                    description: 'Path to uploaded receipt file (required for some categories)'
                )
            ],
            required: ['consultantId', 'description', 'amount', 'currency']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Expense created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                new OA\Property(property: 'amountEur', type: 'string', example: '125.50'),
                new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf'),
                new OA\Property(property: 'validated', type: 'boolean', example: false),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                ),
                new OA\Property(
                    property: 'client',
                    type: 'object',
                    nullable: true,
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                        new OA\Property(property: 'code', type: 'string', example: 'ACME')
                    ]
                ),
                new OA\Property(
                    property: 'trip',
                    type: 'object',
                    nullable: true,
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'title', type: 'string', example: 'Client Meeting in Paris'),
                        new OA\Property(property: 'destination', type: 'string', example: 'Paris, France')
                    ]
                ),
                new OA\Property(
                    property: 'category',
                    type: 'object',
                    nullable: true,
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                        new OA\Property(property: 'code', type: 'string', example: 'TRANSPORT')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Description is required'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The consultant with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $expense = $this->expenseService->createExpense($data);

            // Format the response
            $response = [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'description' => $expense->getDescription(),
                'amount' => $expense->getAmount(),
                'currency' => $expense->getCurrency(),
                'amountEur' => $expense->getAmountEur(),
                'exchangeRate' => $expense->getExchangeRate(),
                'receipt' => $expense->getReceipt(),
                'validated' => $expense->isValidated(),
                'consultant' => [
                    'id' => $expense->getConsultant()->getId(),
                    'firstName' => $expense->getConsultant()->getFirstName(),
                    'lastName' => $expense->getConsultant()->getLastName()
                ],
                'client' => $expense->getClient() ? [
                    'id' => $expense->getClient()->getId(),
                    'name' => $expense->getClient()->getName(),
                    'code' => $expense->getClient()->getCode()
                ] : null,
                'trip' => $expense->getTrip() ? [
                    'id' => $expense->getTrip()->getId(),
                    'title' => $expense->getTrip()->getTitle(),
                    'destination' => $expense->getTrip()->getDestination()
                ] : null,
                'category' => $expense->getCategory() ? [
                    'id' => $expense->getCategory()->getId(),
                    'name' => $expense->getCategory()->getName(),
                    'code' => $expense->getCategory()->getCode()
                ] : null
            ];

            return $this->json($response, 201);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Update an existing expense
     */
    #[Route('/{id}', name: 'app_expense_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Expense ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated expense data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Expense updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                new OA\Property(property: 'amountEur', type: 'string', example: '125.50'),
                new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf'),
                new OA\Property(property: 'validated', type: 'boolean', example: false),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Expense not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Expense not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The expense with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You cannot modify a validated expense'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $expense = $this->expenseService->updateExpense($id, $data);

            // Format the response
            $response = [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'description' => $expense->getDescription(),
                'amount' => $expense->getAmount(),
                'currency' => $expense->getCurrency(),
                'amountEur' => $expense->getAmountEur(),
                'exchangeRate' => $expense->getExchangeRate(),
                'receipt' => $expense->getReceipt(),
                'validated' => $expense->isValidated(),
                'consultant' => [
                    'id' => $expense->getConsultant()->getId(),
                    'firstName' => $expense->getConsultant()->getFirstName(),
                    'lastName' => $expense->getConsultant()->getLastName()
                ]
            ];

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Expense not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\Symfony\Component\Security\Core\Exception\AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Delete an expense
     */
    #[Route('/{id}', name: 'app_expense_delete', methods: ['DELETE'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Expense ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Expense deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'message', type: 'string', example: 'Expense successfully deleted'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Expense not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Expense not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The expense with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You cannot delete a validated expense'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            // Get the expense before deleting it to include in the response
            $expense = $this->expenseService->getExpenseById($id);

            // Store relevant information before deletion
            $response = [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'message' => 'Expense successfully deleted',
                'success' => true
            ];

            // Delete the expense
            $this->expenseService->deleteExpense($id);

            // Return a 200 OK response with information about the deleted entry
            return $this->json($response, 200);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Expense not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\Symfony\Component\Security\Core\Exception\AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Validate an expense (admin only)
     */
    #[Route('/{id}/validate', name: 'app_expense_validate', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Expense ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Expense validated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'description', type: 'string', example: 'Train ticket to client site'),
                new OA\Property(property: 'amount', type: 'string', example: '125.50'),
                new OA\Property(property: 'currency', type: 'string', example: 'EUR'),
                new OA\Property(property: 'amountEur', type: 'string', example: '125.50'),
                new OA\Property(property: 'exchangeRate', type: 'string', example: '1.0000'),
                new OA\Property(property: 'receipt', type: 'string', nullable: true, example: 'receipts/2023/06/15/receipt-123.pdf'),
                new OA\Property(property: 'validated', type: 'boolean', example: true),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Expense not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Expense not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The expense with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Admin access required'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function validate(int $id): JsonResponse
    {
        try {
            $expense = $this->expenseService->validateExpense($id);

            // Format the response
            $response = [
                'id' => $expense->getId(),
                'date' => $expense->getDate()->format('Y-m-d'),
                'description' => $expense->getDescription(),
                'amount' => $expense->getAmount(),
                'currency' => $expense->getCurrency(),
                'amountEur' => $expense->getAmountEur(),
                'exchangeRate' => $expense->getExchangeRate(),
                'receipt' => $expense->getReceipt(),
                'validated' => $expense->isValidated(),
                'consultant' => [
                    'id' => $expense->getConsultant()->getId(),
                    'firstName' => $expense->getConsultant()->getFirstName(),
                    'lastName' => $expense->getConsultant()->getLastName()
                ]
            ];

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Expense not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\Symfony\Component\Security\Core\Exception\AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Calculate total expenses for a consultant
     */
    #[Route('/total/{consultantId}', name: 'app_expense_total', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'Consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the total expenses amount in EUR',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'total', type: 'number', format: 'float', example: 1250.75)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The consultant with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function total(int $consultantId, Request $request): JsonResponse
    {
        try {
            $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
            $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

            $total = $this->expenseService->calculateTotal($consultantId, $startDate, $endDate);
            return $this->json(['total' => $total]);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\Symfony\Component\Security\Core\Exception\AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }
}
