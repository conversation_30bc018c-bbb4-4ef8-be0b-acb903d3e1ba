<?php

namespace App\Controller;

use App\Entity\ExpenseCategory;
use App\Repository\ExpenseCategoryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use OpenApi\Attributes as OA;

/**
 * Controller for managing expense categories
 */
#[Route('/api/v1/expense-categories')]
#[OA\Tag(name: 'Expense Categories')]
class ExpenseCategoryController extends AbstractController
{
    public function __construct(
        private readonly ExpenseCategoryRepository $expenseCategoryRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    /**
     * Get all expense categories
     */
    #[Route('', name: 'app_expense_category_index', methods: ['GET'])]
    #[OA\Parameter(
        name: 'active',
        description: 'Filter by active status',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Parameter(
        name: 'root_only',
        description: 'Get only root categories (no parent)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of expense categories',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                    new OA\Property(property: 'code', type: 'string', example: 'TRANSPORT'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'All transportation related expenses'),
                    new OA\Property(property: 'requiresReceipt', type: 'boolean', example: true),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true),
                    new OA\Property(property: 'fullPath', type: 'string', example: 'Transportation > Flight Tickets'),
                    new OA\Property(property: 'isRootCategory', type: 'boolean', example: true),
                    new OA\Property(
                        property: 'parentCategory',
                        type: 'object',
                        nullable: true,
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                            new OA\Property(property: 'code', type: 'string', example: 'TRANSPORT')
                        ]
                    ),
                    new OA\Property(
                        property: 'subcategories',
                        type: 'array',
                        items: new OA\Items(
                            properties: [
                                new OA\Property(property: 'id', type: 'integer', example: 2),
                                new OA\Property(property: 'name', type: 'string', example: 'Flight Tickets'),
                                new OA\Property(property: 'code', type: 'string', example: 'FLIGHT')
                            ]
                        )
                    )
                ]
            )
        )
    )]
    public function index(Request $request): JsonResponse
    {
        $activeFilter = $request->query->get('active');
        $rootOnly = $request->query->get('root_only');

        if ($rootOnly === 'true') {
            $categories = $this->expenseCategoryRepository->findRootCategories();
        } elseif ($activeFilter !== null) {
            $active = filter_var($activeFilter, FILTER_VALIDATE_BOOLEAN);
            $categories = $active ? $this->expenseCategoryRepository->findActive() : $this->expenseCategoryRepository->findAll();
        } else {
            $categories = $this->expenseCategoryRepository->findAll();
        }

        $response = array_map(fn(ExpenseCategory $category) => [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'code' => $category->getCode(),
            'description' => $category->getDescription(),
            'requiresReceipt' => $category->requiresReceipt(),
            'isActive' => $category->isActive(),
            'fullPath' => $category->getFullPath(),
            'isRootCategory' => $category->isRootCategory(),
            'parentCategory' => $category->getParentCategory() ? [
                'id' => $category->getParentCategory()->getId(),
                'name' => $category->getParentCategory()->getName(),
                'code' => $category->getParentCategory()->getCode()
            ] : null,
            'subcategories' => array_map(fn(ExpenseCategory $sub) => [
                'id' => $sub->getId(),
                'name' => $sub->getName(),
                'code' => $sub->getCode()
            ], $category->getSubcategories()->toArray())
        ], $categories);

        return $this->json($response);
    }

    /**
     * Get a specific expense category
     */
    #[Route('/{id}', name: 'app_expense_category_show', methods: ['GET'])]
    #[OA\Response(response: 200, description: 'Expense category details')]
    #[OA\Response(response: 404, description: 'Category not found')]
    public function show(int $id): JsonResponse
    {
        $category = $this->expenseCategoryRepository->find($id);

        if (!$category) {
            return $this->json([
                'error' => 'Expense category not found',
                'success' => false
            ], 404);
        }

        $response = [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'code' => $category->getCode(),
            'description' => $category->getDescription(),
            'requiresReceipt' => $category->requiresReceipt(),
            'isActive' => $category->isActive(),
            'fullPath' => $category->getFullPath(),
            'isRootCategory' => $category->isRootCategory(),
            'parentCategory' => $category->getParentCategory() ? [
                'id' => $category->getParentCategory()->getId(),
                'name' => $category->getParentCategory()->getName(),
                'code' => $category->getParentCategory()->getCode()
            ] : null,
            'subcategories' => array_map(fn(ExpenseCategory $sub) => [
                'id' => $sub->getId(),
                'name' => $sub->getName(),
                'code' => $sub->getCode(),
                'description' => $sub->getDescription(),
                'requiresReceipt' => $sub->requiresReceipt(),
                'isActive' => $sub->isActive()
            ], $category->getSubcategories()->toArray())
        ];

        return $this->json($response);
    }

    /**
     * Get subcategories of a parent category
     */
    #[Route('/{id}/subcategories', name: 'app_expense_category_subcategories', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'List of subcategories',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 2),
                    new OA\Property(property: 'name', type: 'string', example: 'Flight Tickets'),
                    new OA\Property(property: 'code', type: 'string', example: 'FLIGHT'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Airline tickets and fees'),
                    new OA\Property(property: 'requiresReceipt', type: 'boolean', example: true),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true)
                ]
            )
        )
    )]
    #[OA\Response(response: 404, description: 'Category not found')]
    public function subcategories(int $id): JsonResponse
    {
        $category = $this->expenseCategoryRepository->find($id);

        if (!$category) {
            return $this->json([
                'error' => 'Expense category not found',
                'success' => false
            ], 404);
        }

        $subcategories = $this->expenseCategoryRepository->findSubcategories($category);

        $response = array_map(fn(ExpenseCategory $sub) => [
            'id' => $sub->getId(),
            'name' => $sub->getName(),
            'code' => $sub->getCode(),
            'description' => $sub->getDescription(),
            'requiresReceipt' => $sub->requiresReceipt(),
            'isActive' => $sub->isActive()
        ], $subcategories);

        return $this->json($response);
    }

    /**
     * Create a new expense category (admin only)
     */
    #[Route('', name: 'app_expense_category_create', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Expense category data for creating a new category. Categories can be root categories or subcategories with a parent.',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'name',
                    type: 'string',
                    example: 'Transportation',
                    description: 'Category name'
                ),
                new OA\Property(
                    property: 'code',
                    type: 'string',
                    example: 'TRANSPORT',
                    description: 'Unique category code (uppercase, no spaces)'
                ),
                new OA\Property(
                    property: 'description',
                    type: 'string',
                    nullable: true,
                    example: 'All transportation related expenses',
                    description: 'Category description'
                ),
                new OA\Property(
                    property: 'requiresReceipt',
                    type: 'boolean',
                    example: true,
                    description: 'Whether expenses in this category require receipts'
                ),
                new OA\Property(
                    property: 'parentCategoryId',
                    type: 'integer',
                    nullable: true,
                    example: 1,
                    description: 'ID of parent category (null for root categories)'
                ),
                new OA\Property(
                    property: 'isActive',
                    type: 'boolean',
                    example: true,
                    description: 'Whether the category is active'
                )
            ],
            required: ['name', 'code']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Expense category created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                new OA\Property(property: 'code', type: 'string', example: 'TRANSPORT'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'All transportation related expenses'),
                new OA\Property(property: 'requiresReceipt', type: 'boolean', example: true),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'fullPath', type: 'string', example: 'Transportation'),
                new OA\Property(property: 'isRootCategory', type: 'boolean', example: true),
                new OA\Property(
                    property: 'parentCategory',
                    type: 'object',
                    nullable: true,
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                        new OA\Property(property: 'code', type: 'string', example: 'TRANSPORT')
                    ]
                ),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(response: 400, description: 'Invalid data provided')]
    #[OA\Response(response: 401, description: 'Authentication required')]
    #[OA\Response(response: 403, description: 'Admin access required')]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!isset($data['name']) || !isset($data['code'])) {
                return $this->json([
                    'error' => 'Name and code are required',
                    'success' => false
                ], 400);
            }

            // Check if code already exists
            if ($this->expenseCategoryRepository->findByCode($data['code'])) {
                return $this->json([
                    'error' => 'Category code already exists',
                    'success' => false
                ], 400);
            }

            $category = new ExpenseCategory();
            $category->setName($data['name']);
            $category->setCode($data['code']);
            $category->setDescription($data['description'] ?? null);
            $category->setRequiresReceipt($data['requiresReceipt'] ?? false);
            $category->setIsActive($data['isActive'] ?? true);

            // Set parent category if provided
            if (isset($data['parentCategoryId'])) {
                $parentCategory = $this->expenseCategoryRepository->find($data['parentCategoryId']);
                if ($parentCategory) {
                    $category->setParentCategory($parentCategory);
                } else {
                    return $this->json([
                        'error' => 'Parent category not found',
                        'success' => false
                    ], 400);
                }
            }

            $this->entityManager->persist($category);
            $this->entityManager->flush();

            $response = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'code' => $category->getCode(),
                'description' => $category->getDescription(),
                'requiresReceipt' => $category->requiresReceipt(),
                'isActive' => $category->isActive(),
                'fullPath' => $category->getFullPath(),
                'isRootCategory' => $category->isRootCategory(),
                'parentCategory' => $category->getParentCategory() ? [
                    'id' => $category->getParentCategory()->getId(),
                    'name' => $category->getParentCategory()->getName(),
                    'code' => $category->getParentCategory()->getCode()
                ] : null,
                'success' => true
            ];

            return $this->json($response, 201);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to create expense category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Update an expense category (admin only)
     */
    #[Route('/{id}', name: 'app_expense_category_update', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Expense category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated expense category data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                new OA\Property(property: 'code', type: 'string', example: 'TRANSPORT'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'All transportation related expenses'),
                new OA\Property(property: 'requiresReceipt', type: 'boolean', example: true),
                new OA\Property(property: 'parentCategoryId', type: 'integer', nullable: true, example: 1),
                new OA\Property(property: 'isActive', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(response: 200, description: 'Category updated successfully')]
    #[OA\Response(response: 400, description: 'Invalid data')]
    #[OA\Response(response: 404, description: 'Category not found')]
    #[OA\Response(response: 403, description: 'Admin access required')]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $category = $this->expenseCategoryRepository->find($id);

            if (!$category) {
                return $this->json([
                    'error' => 'Expense category not found',
                    'success' => false
                ], 404);
            }

            $data = json_decode($request->getContent(), true);

            // Check if code already exists (excluding current category)
            if (isset($data['code']) && $data['code'] !== $category->getCode()) {
                $existingCategory = $this->expenseCategoryRepository->findByCode($data['code']);
                if ($existingCategory) {
                    return $this->json([
                        'error' => 'Category code already exists',
                        'success' => false
                    ], 400);
                }
            }

            // Update fields
            if (isset($data['name'])) {
                $category->setName($data['name']);
            }
            if (isset($data['code'])) {
                $category->setCode($data['code']);
            }
            if (isset($data['description'])) {
                $category->setDescription($data['description']);
            }
            if (isset($data['requiresReceipt'])) {
                $category->setRequiresReceipt($data['requiresReceipt']);
            }
            if (isset($data['isActive'])) {
                $category->setIsActive($data['isActive']);
            }

            // Update parent category
            if (array_key_exists('parentCategoryId', $data)) {
                if ($data['parentCategoryId'] === null) {
                    $category->setParentCategory(null);
                } else {
                    $parentCategory = $this->expenseCategoryRepository->find($data['parentCategoryId']);
                    if ($parentCategory) {
                        // Prevent circular references
                        if ($parentCategory->getId() === $category->getId()) {
                            return $this->json([
                                'error' => 'Category cannot be its own parent',
                                'success' => false
                            ], 400);
                        }
                        $category->setParentCategory($parentCategory);
                    } else {
                        return $this->json([
                            'error' => 'Parent category not found',
                            'success' => false
                        ], 400);
                    }
                }
            }

            $this->entityManager->flush();

            $response = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'code' => $category->getCode(),
                'description' => $category->getDescription(),
                'requiresReceipt' => $category->requiresReceipt(),
                'isActive' => $category->isActive(),
                'fullPath' => $category->getFullPath(),
                'isRootCategory' => $category->isRootCategory(),
                'parentCategory' => $category->getParentCategory() ? [
                    'id' => $category->getParentCategory()->getId(),
                    'name' => $category->getParentCategory()->getName(),
                    'code' => $category->getParentCategory()->getCode()
                ] : null,
                'success' => true
            ];

            return $this->json($response);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to update expense category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Delete an expense category (admin only)
     */
    #[Route('/{id}', name: 'app_expense_category_delete', methods: ['DELETE'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Expense category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Category deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Transportation'),
                new OA\Property(property: 'message', type: 'string', example: 'Expense category successfully deleted'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(response: 404, description: 'Category not found')]
    #[OA\Response(response: 400, description: 'Cannot delete category with subcategories or expenses')]
    #[OA\Response(response: 403, description: 'Admin access required')]
    public function delete(int $id): JsonResponse
    {
        try {
            $category = $this->expenseCategoryRepository->find($id);

            if (!$category) {
                return $this->json([
                    'error' => 'Expense category not found',
                    'success' => false
                ], 404);
            }

            // Check if category has subcategories
            if (!$category->getSubcategories()->isEmpty()) {
                return $this->json([
                    'error' => 'Cannot delete category with subcategories',
                    'message' => 'Please delete or reassign subcategories first',
                    'success' => false
                ], 400);
            }

            // Check if category has expenses
            if (!$category->getExpenses()->isEmpty()) {
                return $this->json([
                    'error' => 'Cannot delete category with associated expenses',
                    'message' => 'Please reassign expenses to another category first',
                    'success' => false
                ], 400);
            }

            // Store information before deletion
            $response = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'message' => 'Expense category successfully deleted',
                'success' => true
            ];

            $this->entityManager->remove($category);
            $this->entityManager->flush();

            return $this->json($response);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to delete expense category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
