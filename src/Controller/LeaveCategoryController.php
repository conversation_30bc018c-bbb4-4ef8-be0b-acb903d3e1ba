<?php

namespace App\Controller;

use App\Entity\LeaveCategory;
use App\Repository\LeaveCategoryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use OpenApi\Attributes as OA;

/**
 * Controller for managing leave categories (admin only)
 */
#[Route('/api/v1/leave-categories')]
#[OA\Tag(name: 'Leave Categories')]
class LeaveCategoryController extends AbstractController
{
    public function __construct(
        private readonly LeaveCategoryRepository $leaveCategoryRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    /**
     * Get all leave categories
     */
    #[Route('', name: 'app_leave_category_index', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns all leave categories',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                    new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                    new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true),
                    new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                    new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
                ]
            )
        )
    )]
    public function index(): JsonResponse
    {
        $categories = $this->leaveCategoryRepository->findAllOrdered();

        $response = array_map(fn(LeaveCategory $category) => [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'code' => $category->getCode(),
            'description' => $category->getDescription(),
            'requiresDescription' => $category->isRequiresDescription(),
            'isActive' => $category->isActive(),
            'isPaid' => $category->isPaid(),
            'sortOrder' => $category->getSortOrder()
        ], $categories);

        return $this->json($response);
    }

    /**
     * Get active leave categories only
     */
    #[Route('/active', name: 'app_leave_category_active', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns active leave categories only',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                    new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                    new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                    new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                    new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
                ]
            )
        )
    )]
    public function active(): JsonResponse
    {
        $categories = $this->leaveCategoryRepository->findActiveCategories();

        $response = array_map(fn(LeaveCategory $category) => [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'code' => $category->getCode(),
            'description' => $category->getDescription(),
            'requiresDescription' => $category->isRequiresDescription(),
            'isPaid' => $category->isPaid(),
            'sortOrder' => $category->getSortOrder()
        ], $categories);

        return $this->json($response);
    }

    /**
     * Get a specific leave category
     */
    #[Route('/{id}', name: 'app_leave_category_show', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the leave category',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Leave category not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Leave category not found')
            ]
        )
    )]
    public function show(int $id): JsonResponse
    {
        $category = $this->leaveCategoryRepository->find($id);

        if (!$category) {
            throw new NotFoundHttpException('Leave category not found');
        }

        $response = [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'code' => $category->getCode(),
            'description' => $category->getDescription(),
            'requiresDescription' => $category->isRequiresDescription(),
            'isActive' => $category->isActive(),
            'isPaid' => $category->isPaid(),
            'sortOrder' => $category->getSortOrder()
        ];

        return $this->json($response);
    }

    /**
     * Create a new leave category (admin only)
     */
    #[Route('', name: 'app_leave_category_create', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Leave category data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ],
            required: ['name', 'code']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Leave category created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ]
        )
    )]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            // Validate required fields
            if (empty($data['name']) || empty($data['code'])) {
                return $this->json([
                    'error' => 'Name and code are required',
                    'success' => false
                ], 400);
            }

            // Check if code already exists
            $existingCategory = $this->leaveCategoryRepository->findByCode($data['code']);
            if ($existingCategory) {
                return $this->json([
                    'error' => 'Category code already exists',
                    'success' => false
                ], 400);
            }

            $category = new LeaveCategory();
            $category->setName($data['name']);
            $category->setCode($data['code']);

            if (isset($data['description'])) {
                $category->setDescription($data['description']);
            }

            $category->setRequiresDescription($data['requiresDescription'] ?? false);
            $category->setIsActive($data['isActive'] ?? true);
            $category->setIsPaid($data['isPaid'] ?? true);
            $category->setSortOrder($data['sortOrder'] ?? 0);

            $this->entityManager->persist($category);
            $this->entityManager->flush();

            $response = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'code' => $category->getCode(),
                'description' => $category->getDescription(),
                'requiresDescription' => $category->isRequiresDescription(),
                'isActive' => $category->isActive(),
                'isPaid' => $category->isPaid(),
                'sortOrder' => $category->getSortOrder()
            ];

            return $this->json($response, 201);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to create leave category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Update a leave category (admin only)
     */
    #[Route('/{id}', name: 'app_leave_category_update', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated leave category data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave category updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ]
        )
    )]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $category = $this->leaveCategoryRepository->find($id);

            if (!$category) {
                throw new NotFoundHttpException('Leave category not found');
            }

            $data = json_decode($request->getContent(), true);

            // Check if code already exists (excluding current category)
            if (isset($data['code']) && $data['code'] !== $category->getCode()) {
                $existingCategory = $this->leaveCategoryRepository->findByCode($data['code']);
                if ($existingCategory) {
                    return $this->json([
                        'error' => 'Category code already exists',
                        'success' => false
                    ], 400);
                }
            }

            // Update fields
            if (isset($data['name'])) {
                $category->setName($data['name']);
            }

            if (isset($data['code'])) {
                $category->setCode($data['code']);
            }

            if (isset($data['description'])) {
                $category->setDescription($data['description']);
            }

            if (isset($data['requiresDescription'])) {
                $category->setRequiresDescription($data['requiresDescription']);
            }

            if (isset($data['isActive'])) {
                $category->setIsActive($data['isActive']);
            }

            if (isset($data['isPaid'])) {
                $category->setIsPaid($data['isPaid']);
            }

            if (isset($data['sortOrder'])) {
                $category->setSortOrder($data['sortOrder']);
            }

            $this->entityManager->flush();

            $response = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'code' => $category->getCode(),
                'description' => $category->getDescription(),
                'requiresDescription' => $category->isRequiresDescription(),
                'isActive' => $category->isActive(),
                'isPaid' => $category->isPaid(),
                'sortOrder' => $category->getSortOrder()
            ];

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave category not found',
                'success' => false
            ], 404);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to update leave category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Delete a leave category (admin only)
     */
    #[Route('/{id}', name: 'app_leave_category_delete', methods: ['DELETE'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave category deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Leave category deleted successfully'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Cannot delete category with existing leaves',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Cannot delete category with existing leaves'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            $category = $this->leaveCategoryRepository->find($id);

            if (!$category) {
                throw new NotFoundHttpException('Leave category not found');
            }

            // Check if category has associated leaves
            if (!$category->getLeaves()->isEmpty()) {
                return $this->json([
                    'error' => 'Cannot delete category with existing leaves',
                    'success' => false
                ], 400);
            }

            $this->entityManager->remove($category);
            $this->entityManager->flush();

            return $this->json([
                'message' => 'Leave category deleted successfully',
                'success' => true
            ]);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave category not found',
                'success' => false
            ], 404);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to delete leave category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}