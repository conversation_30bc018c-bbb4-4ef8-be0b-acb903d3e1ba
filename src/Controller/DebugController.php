<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/debug')]
class DebugController extends AbstractController
{
    #[Route('/user', name: 'app_debug_user', methods: ['GET'])]
    public function debugUser(): JsonResponse
    {
        $user = $this->getUser();
        
        if (!$user) {
            return $this->json([
                'message' => 'No authenticated user',
                'authenticated' => false
            ]);
        }
        
        return $this->json([
            'authenticated' => true,
            'user_class' => get_class($user),
            'user_identifier' => $user->getUserIdentifier(),
            'roles' => $user->getRoles(),
            'methods' => get_class_methods($user)
        ]);
    }
}
