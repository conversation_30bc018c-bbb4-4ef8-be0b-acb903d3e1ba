<?php

namespace App\Controller;

use App\Entity\Trip;
use App\Repository\TripRepository;
use App\Repository\ClientRepository;
use App\Repository\ConsultantRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;
use OpenApi\Attributes as OA;

/**
 * Controller for managing trips
 */
#[Route('/api/v1/trips')]
#[OA\Tag(name: 'Trips')]
class TripController extends AbstractController
{
    public function __construct(
        private readonly TripRepository $tripRepository,
        private readonly ClientRepository $clientRepository,
        private readonly ConsultantRepository $consultantRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly Security $security
    ) {}

    /**
     * Get all trips (admin) or consultant's trips
     */
    #[Route('', name: 'app_trip_index', methods: ['GET'])]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date filter (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date filter (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of trips',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'title', type: 'string', example: 'Client Meeting in Paris'),
                    new OA\Property(property: 'destination', type: 'string', nullable: true, example: 'Paris, France'),
                    new OA\Property(property: 'purpose', type: 'string', nullable: true, example: 'Client consultation'),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-06-17'),
                    new OA\Property(property: 'notes', type: 'string', nullable: true, example: 'Important project kickoff'),
                    new OA\Property(property: 'totalExpenses', type: 'number', format: 'float', example: 1250.50),
                    new OA\Property(property: 'durationInDays', type: 'integer', example: 3),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                        ]
                    ),
                    new OA\Property(
                        property: 'client',
                        type: 'object',
                        nullable: true,
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                            new OA\Property(property: 'code', type: 'string', example: 'ACME')
                        ]
                    )
                ]
            )
        )
    )]
    public function index(Request $request): JsonResponse
    {
        $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

        $user = $this->security->getUser();
        $consultant = $this->consultantRepository->find($user->getId());

        if ($this->isGranted('ROLE_ADMIN')) {
            // Admin can see all trips
            $trips = $this->tripRepository->findAll();
        } else {
            // Consultant can only see their own trips
            $trips = $this->tripRepository->findByConsultant($consultant, $startDate, $endDate);
        }

        $response = array_map(fn(Trip $trip) => [
            'id' => $trip->getId(),
            'title' => $trip->getTitle(),
            'destination' => $trip->getDestination(),
            'purpose' => $trip->getPurpose(),
            'startDate' => $trip->getStartDate()->format('Y-m-d'),
            'endDate' => $trip->getEndDate()->format('Y-m-d'),
            'notes' => $trip->getNotes(),
            'totalExpenses' => $trip->getTotalExpenses(),
            'durationInDays' => $trip->getDurationInDays(),
            'consultant' => [
                'id' => $trip->getConsultant()->getId(),
                'firstName' => $trip->getConsultant()->getFirstName(),
                'lastName' => $trip->getConsultant()->getLastName()
            ],
            'client' => $trip->getClient() ? [
                'id' => $trip->getClient()->getId(),
                'name' => $trip->getClient()->getName(),
                'code' => $trip->getClient()->getCode()
            ] : null
        ], $trips);

        return $this->json($response);
    }

    /**
     * Get a specific trip
     */
    #[Route('/{id}', name: 'app_trip_show', methods: ['GET'])]
    #[OA\Response(response: 200, description: 'Trip details')]
    #[OA\Response(response: 404, description: 'Trip not found')]
    #[OA\Response(response: 403, description: 'Access denied')]
    public function show(int $id): JsonResponse
    {
        $trip = $this->tripRepository->find($id);

        if (!$trip) {
            return $this->json([
                'error' => 'Trip not found',
                'success' => false
            ], 404);
        }

        $user = $this->security->getUser();
        $consultant = $this->consultantRepository->find($user->getId());

        // Check access: admin or trip owner
        if (!$this->isGranted('ROLE_ADMIN') && $trip->getConsultant()->getId() !== $consultant->getId()) {
            return $this->json([
                'error' => 'Access denied',
                'success' => false
            ], 403);
        }

        $response = [
            'id' => $trip->getId(),
            'title' => $trip->getTitle(),
            'destination' => $trip->getDestination(),
            'purpose' => $trip->getPurpose(),
            'startDate' => $trip->getStartDate()->format('Y-m-d'),
            'endDate' => $trip->getEndDate()->format('Y-m-d'),
            'notes' => $trip->getNotes(),
            'totalExpenses' => $trip->getTotalExpenses(),
            'durationInDays' => $trip->getDurationInDays(),
            'consultant' => [
                'id' => $trip->getConsultant()->getId(),
                'firstName' => $trip->getConsultant()->getFirstName(),
                'lastName' => $trip->getConsultant()->getLastName()
            ],
            'client' => $trip->getClient() ? [
                'id' => $trip->getClient()->getId(),
                'name' => $trip->getClient()->getName(),
                'code' => $trip->getClient()->getCode()
            ] : null
        ];

        return $this->json($response);
    }

    /**
     * Create a new trip
     */
    #[Route('', name: 'app_trip_create', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Trip data for creating a new business trip. This endpoint allows consultants to create trips that can group multiple related expenses together for better organization and reporting.',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'title',
                    type: 'string',
                    example: 'Client Meeting in Paris',
                    description: 'Descriptive title for the trip'
                ),
                new OA\Property(
                    property: 'destination',
                    type: 'string',
                    nullable: true,
                    example: 'Paris, France',
                    description: 'Trip destination city/country'
                ),
                new OA\Property(
                    property: 'purpose',
                    type: 'string',
                    nullable: true,
                    example: 'Quarterly business review with client',
                    description: 'Business purpose of the trip'
                ),
                new OA\Property(
                    property: 'startDate',
                    type: 'string',
                    format: 'date',
                    example: '2023-06-15',
                    description: 'Trip start date (format: Y-m-d)'
                ),
                new OA\Property(
                    property: 'endDate',
                    type: 'string',
                    format: 'date',
                    example: '2023-06-17',
                    description: 'Trip end date (format: Y-m-d)'
                ),
                new OA\Property(
                    property: 'clientId',
                    type: 'integer',
                    nullable: true,
                    example: 1,
                    description: 'ID of the client associated with this trip (optional)'
                ),
                new OA\Property(
                    property: 'notes',
                    type: 'string',
                    nullable: true,
                    example: 'Important Q2 review meeting with key stakeholders',
                    description: 'Additional notes about the trip'
                )
            ],
            required: ['title', 'startDate', 'endDate']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Trip created successfully. The response includes the complete trip information with calculated fields like duration and total expenses (initially 0).',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'title', type: 'string', example: 'Client Meeting in Paris'),
                new OA\Property(property: 'destination', type: 'string', nullable: true, example: 'Paris, France'),
                new OA\Property(property: 'purpose', type: 'string', nullable: true, example: 'Quarterly business review with client'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-06-17'),
                new OA\Property(property: 'notes', type: 'string', nullable: true, example: 'Important Q2 review meeting'),
                new OA\Property(property: 'totalExpenses', type: 'number', format: 'float', example: 0.0),
                new OA\Property(property: 'durationInDays', type: 'integer', example: 3),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Smith')
                    ]
                ),
                new OA\Property(
                    property: 'client',
                    type: 'object',
                    nullable: true,
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                        new OA\Property(property: 'code', type: 'string', example: 'ACME')
                    ]
                ),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(response: 400, description: 'Invalid data provided')]
    #[OA\Response(response: 401, description: 'Authentication required')]
    #[OA\Response(response: 403, description: 'Access denied')]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!isset($data['title']) || !isset($data['startDate']) || !isset($data['endDate'])) {
                return $this->json([
                    'error' => 'Title, start date, and end date are required',
                    'success' => false
                ], 400);
            }

            $user = $this->security->getUser();
            $consultant = $this->consultantRepository->find($user->getId());

            if (!$consultant) {
                return $this->json([
                    'error' => 'Consultant not found',
                    'success' => false
                ], 404);
            }

            // Validate dates
            $startDate = new \DateTime($data['startDate']);
            $endDate = new \DateTime($data['endDate']);

            if ($startDate > $endDate) {
                return $this->json([
                    'error' => 'Start date cannot be after end date',
                    'success' => false
                ], 400);
            }

            $trip = new Trip();
            $trip->setConsultant($consultant);
            $trip->setTitle($data['title']);
            $trip->setDestination($data['destination'] ?? null);
            $trip->setPurpose($data['purpose'] ?? null);
            $trip->setStartDate($startDate);
            $trip->setEndDate($endDate);
            $trip->setNotes($data['notes'] ?? null);

            // Set client if provided
            if (isset($data['clientId'])) {
                $client = $this->clientRepository->find($data['clientId']);
                if ($client) {
                    $trip->setClient($client);
                }
            }

            $this->entityManager->persist($trip);
            $this->entityManager->flush();

            $response = [
                'id' => $trip->getId(),
                'title' => $trip->getTitle(),
                'destination' => $trip->getDestination(),
                'purpose' => $trip->getPurpose(),
                'startDate' => $trip->getStartDate()->format('Y-m-d'),
                'endDate' => $trip->getEndDate()->format('Y-m-d'),
                'notes' => $trip->getNotes(),
                'totalExpenses' => $trip->getTotalExpenses(),
                'durationInDays' => $trip->getDurationInDays(),
                'consultant' => [
                    'id' => $trip->getConsultant()->getId(),
                    'firstName' => $trip->getConsultant()->getFirstName(),
                    'lastName' => $trip->getConsultant()->getLastName()
                ],
                'client' => $trip->getClient() ? [
                    'id' => $trip->getClient()->getId(),
                    'name' => $trip->getClient()->getName(),
                    'code' => $trip->getClient()->getCode()
                ] : null,
                'success' => true
            ];

            return $this->json($response, 201);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to create trip: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Update a trip
     */
    #[Route('/{id}', name: 'app_trip_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Trip ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Trip data for updating an existing business trip. Consultants can only update their own trips, while administrators can update any trip.',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'title',
                    type: 'string',
                    example: 'Updated Client Meeting in Paris',
                    description: 'Descriptive title for the trip'
                ),
                new OA\Property(
                    property: 'destination',
                    type: 'string',
                    nullable: true,
                    example: 'Paris, France',
                    description: 'Trip destination city/country'
                ),
                new OA\Property(
                    property: 'purpose',
                    type: 'string',
                    nullable: true,
                    example: 'Updated quarterly business review with client',
                    description: 'Business purpose of the trip'
                ),
                new OA\Property(
                    property: 'startDate',
                    type: 'string',
                    format: 'date',
                    example: '2023-06-15',
                    description: 'Trip start date (format: Y-m-d)'
                ),
                new OA\Property(
                    property: 'endDate',
                    type: 'string',
                    format: 'date',
                    example: '2023-06-17',
                    description: 'Trip end date (format: Y-m-d)'
                ),
                new OA\Property(
                    property: 'clientId',
                    type: 'integer',
                    nullable: true,
                    example: 1,
                    description: 'ID of the client associated with this trip (optional)'
                ),
                new OA\Property(
                    property: 'notes',
                    type: 'string',
                    nullable: true,
                    example: 'Updated notes about the trip',
                    description: 'Additional notes or comments about the trip'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Trip updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'title', type: 'string', example: 'Updated Client Meeting in Paris'),
                new OA\Property(property: 'destination', type: 'string', nullable: true, example: 'Paris, France'),
                new OA\Property(property: 'purpose', type: 'string', nullable: true, example: 'Updated quarterly business review'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-06-17'),
                new OA\Property(property: 'notes', type: 'string', nullable: true, example: 'Updated notes'),
                new OA\Property(property: 'totalExpenses', type: 'string', example: '0.00'),
                new OA\Property(property: 'durationInDays', type: 'integer', example: 3),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                ),
                new OA\Property(
                    property: 'client',
                    type: 'object',
                    nullable: true,
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                        new OA\Property(property: 'code', type: 'string', example: 'ACME')
                    ]
                ),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(response: 400, description: 'Invalid data provided')]
    #[OA\Response(response: 401, description: 'Authentication required')]
    #[OA\Response(response: 403, description: 'Access denied - can only update own trips')]
    #[OA\Response(response: 404, description: 'Trip not found')]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $trip = $this->tripRepository->find($id);

            if (!$trip) {
                return $this->json([
                    'error' => 'Trip not found',
                    'success' => false
                ], 404);
            }

            $user = $this->security->getUser();
            $consultant = $this->consultantRepository->find($user->getId());

            // Check access: admin or trip owner
            if (!$this->isGranted('ROLE_ADMIN') && $trip->getConsultant()->getId() !== $consultant->getId()) {
                return $this->json([
                    'error' => 'You can only update your own trips',
                    'success' => false
                ], 403);
            }

            $data = json_decode($request->getContent(), true);

            // Update trip properties if provided
            if (isset($data['title'])) {
                $trip->setTitle($data['title']);
            }

            if (isset($data['destination'])) {
                $trip->setDestination($data['destination']);
            }

            if (isset($data['purpose'])) {
                $trip->setPurpose($data['purpose']);
            }

            if (isset($data['notes'])) {
                $trip->setNotes($data['notes']);
            }

            // Update dates if provided
            if (isset($data['startDate'])) {
                $startDate = new \DateTime($data['startDate']);
                $trip->setStartDate($startDate);
            }

            if (isset($data['endDate'])) {
                $endDate = new \DateTime($data['endDate']);
                $trip->setEndDate($endDate);
            }

            // Validate dates if both are set
            if ($trip->getStartDate() > $trip->getEndDate()) {
                return $this->json([
                    'error' => 'Start date cannot be after end date',
                    'success' => false
                ], 400);
            }

            // Update client if provided
            if (isset($data['clientId'])) {
                if ($data['clientId'] === null) {
                    $trip->setClient(null);
                } else {
                    $client = $this->clientRepository->find($data['clientId']);
                    if (!$client) {
                        return $this->json([
                            'error' => 'Client not found',
                            'success' => false
                        ], 400);
                    }
                    $trip->setClient($client);
                }
            }

            $this->entityManager->flush();

            $response = [
                'id' => $trip->getId(),
                'title' => $trip->getTitle(),
                'destination' => $trip->getDestination(),
                'purpose' => $trip->getPurpose(),
                'startDate' => $trip->getStartDate()->format('Y-m-d'),
                'endDate' => $trip->getEndDate()->format('Y-m-d'),
                'notes' => $trip->getNotes(),
                'totalExpenses' => $trip->getTotalExpenses(),
                'durationInDays' => $trip->getDurationInDays(),
                'consultant' => [
                    'id' => $trip->getConsultant()->getId(),
                    'firstName' => $trip->getConsultant()->getFirstName(),
                    'lastName' => $trip->getConsultant()->getLastName()
                ],
                'client' => $trip->getClient() ? [
                    'id' => $trip->getClient()->getId(),
                    'name' => $trip->getClient()->getName(),
                    'code' => $trip->getClient()->getCode()
                ] : null,
                'success' => true
            ];

            return $this->json($response, 200);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to update trip: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get all trips for the current user
     *
     * This endpoint allows consultants to view their own trips without specifying their consultant ID.
     * Supports the same filtering options as the main trips endpoint.
     */
    #[Route('/my', name: 'app_trip_my', methods: ['GET'])]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date filter (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date filter (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of current user trips',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'title', type: 'string', example: 'Client Meeting in Paris'),
                    new OA\Property(property: 'destination', type: 'string', nullable: true, example: 'Paris, France'),
                    new OA\Property(property: 'purpose', type: 'string', nullable: true, example: 'Client consultation'),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-06-17'),
                    new OA\Property(property: 'notes', type: 'string', nullable: true, example: 'Important project kickoff'),
                    new OA\Property(property: 'totalExpenses', type: 'number', format: 'float', example: 1250.50),
                    new OA\Property(property: 'durationInDays', type: 'integer', example: 3),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                        ]
                    ),
                    new OA\Property(
                        property: 'client',
                        type: 'object',
                        nullable: true,
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Acme Corporation'),
                            new OA\Property(property: 'code', type: 'string', example: 'ACME')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(response: 401, description: 'Authentication required')]
    public function my(Request $request): JsonResponse
    {
        try {
            // Get current user's consultant
            $user = $this->security->getUser();
            $consultant = $this->consultantRepository->find($user->getId());

            if (!$consultant) {
                return $this->json([
                    'error' => 'Consultant not found',
                    'success' => false
                ], 404);
            }

            // Parse query parameters
            $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
            $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

            // Get trips for the current consultant
            $trips = $this->tripRepository->findByConsultant($consultant, $startDate, $endDate);

            $response = array_map(fn(Trip $trip) => [
                'id' => $trip->getId(),
                'title' => $trip->getTitle(),
                'destination' => $trip->getDestination(),
                'purpose' => $trip->getPurpose(),
                'startDate' => $trip->getStartDate()->format('Y-m-d'),
                'endDate' => $trip->getEndDate()->format('Y-m-d'),
                'notes' => $trip->getNotes(),
                'totalExpenses' => $trip->getTotalExpenses(),
                'durationInDays' => $trip->getDurationInDays(),
                'consultant' => [
                    'id' => $trip->getConsultant()->getId(),
                    'firstName' => $trip->getConsultant()->getFirstName(),
                    'lastName' => $trip->getConsultant()->getLastName()
                ],
                'client' => $trip->getClient() ? [
                    'id' => $trip->getClient()->getId(),
                    'name' => $trip->getClient()->getName(),
                    'code' => $trip->getClient()->getCode()
                ] : null
            ], $trips);

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to retrieve trips: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Delete a trip
     */
    #[Route('/{id}', name: 'app_trip_delete', methods: ['DELETE'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Trip ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Trip deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'title', type: 'string', example: 'Client Meeting in Paris'),
                new OA\Property(property: 'message', type: 'string', example: 'Trip successfully deleted'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Trip not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Trip not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The trip with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You can only delete your own trips'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Cannot delete trip with expenses',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Cannot delete trip with associated expenses'),
                new OA\Property(property: 'message', type: 'string', example: 'Please delete or reassign expenses first'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            $trip = $this->tripRepository->find($id);

            if (!$trip) {
                return $this->json([
                    'error' => 'Trip not found',
                    'success' => false
                ], 404);
            }

            $user = $this->security->getUser();
            $consultant = $this->consultantRepository->find($user->getId());

            // Check access: admin or trip owner
            if (!$this->isGranted('ROLE_ADMIN') && $trip->getConsultant()->getId() !== $consultant->getId()) {
                return $this->json([
                    'error' => 'You can only delete your own trips',
                    'success' => false
                ], 403);
            }

            // Check if trip has expenses
            if (!$trip->getExpenses()->isEmpty()) {
                return $this->json([
                    'error' => 'Cannot delete trip with associated expenses',
                    'message' => 'Please delete or reassign expenses first',
                    'success' => false
                ], 400);
            }

            // Store trip information before deletion
            $response = [
                'id' => $trip->getId(),
                'title' => $trip->getTitle(),
                'message' => 'Trip successfully deleted',
                'success' => true
            ];

            // Delete the trip
            $this->entityManager->remove($trip);
            $this->entityManager->flush();

            return $this->json($response, 200);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to delete trip: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
