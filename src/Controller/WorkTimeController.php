<?php

namespace App\Controller;

use App\Service\WorkTimeService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use OpenApi\Attributes as OA;
use App\Entity\WorkTime;

/**
 * Controller for managing work time entries
 */
#[Route('/api/v1/work-time')]
#[OA\Tag(name: 'Work Time')]
class WorkTimeController extends AbstractController
{
    public function __construct(
        private readonly WorkTimeService $workTimeService
    ) {}

    /**
     * Get all work time entries (admin only)
     */
    #[Route('', name: 'app_work_time_index', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns all work time entries',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'hours', type: 'number', format: 'float', example: 7.5),
                    new OA\Property(property: 'remoteWork', type: 'boolean', example: true),
                    new OA\Property(property: 'activity', type: 'string', example: 'Development'),
                    new OA\Property(property: 'comment', type: 'string', example: 'Working on API documentation'),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function index(Request $request): JsonResponse
    {
        $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

        $workTimes = $this->workTimeService->getAllWorkTimes($startDate, $endDate);

        // Create a proper response with all the necessary data
        $response = array_map(fn(WorkTime $workTime) => [
            'id' => $workTime->getId(),
            'date' => $workTime->getDate()->format('Y-m-d'),
            'hours' => $workTime->getHours(),
            'remoteWork' => $workTime->isRemoteWork(),
            'activity' => $workTime->getActivity(),
            'comment' => $workTime->getComment(),
            'consultant' => [
                'id' => $workTime->getConsultant()->getId(),
                'firstName' => $workTime->getConsultant()->getFirstName(),
                'lastName' => $workTime->getConsultant()->getLastName()
            ]
        ], $workTimes);

        return $this->json($response);
    }

    /**
     * Get a specific work time entry
     */
    #[Route('/{id}', name: 'app_work_time_show', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Work time entry ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the work time entry',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'hours', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'remoteWork', type: 'boolean', example: true),
                new OA\Property(property: 'activity', type: 'string', example: 'Development'),
                new OA\Property(property: 'comment', type: 'string', example: 'Working on API documentation'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Work time entry not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Work time entry not found')
            ]
        )
    )]
    public function show(int $id): JsonResponse
    {
        try {
            $workTime = $this->workTimeService->getWorkTimeById($id);

            // Create a proper response with all the necessary data
            $response = [
                'id' => $workTime->getId(),
                'date' => $workTime->getDate()->format('Y-m-d'),
                'hours' => $workTime->getHours(),
                'remoteWork' => $workTime->isRemoteWork(),
                'activity' => $workTime->getActivity(),
                'comment' => $workTime->getComment(),
                'consultant' => [
                    'id' => $workTime->getConsultant()->getId(),
                    'firstName' => $workTime->getConsultant()->getFirstName(),
                    'lastName' => $workTime->getConsultant()->getLastName()
                ]
            ];

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            // Return a 404 Not Found response with an error message
            return $this->json([
                'error' => 'Work time entry not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Get work time entries for a specific consultant
     */
    #[Route('/consultant/{consultantId}', name: 'app_work_time_by_consultant', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'Consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns work time entries for the consultant',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                    new OA\Property(property: 'hours', type: 'number', format: 'float', example: 7.5),
                    new OA\Property(property: 'remoteWork', type: 'boolean', example: true),
                    new OA\Property(property: 'activity', type: 'string', example: 'Development'),
                    new OA\Property(property: 'comment', type: 'string', example: 'Working on API documentation')
                ]
            )
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found')
            ]
        )
    )]
    public function byConsultant(int $consultantId, Request $request): JsonResponse
    {
        try {
            $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
            $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

            $workTimes = $this->workTimeService->getWorkTimesByConsultant($consultantId, $startDate, $endDate);

            // Create a proper response with all the necessary data
            $response = array_map(fn(WorkTime $workTime) => [
                'id' => $workTime->getId(),
                'date' => $workTime->getDate()->format('Y-m-d'),
                'hours' => $workTime->getHours(),
                'remoteWork' => $workTime->isRemoteWork(),
                'activity' => $workTime->getActivity(),
                'comment' => $workTime->getComment()
            ], $workTimes);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            // Return a 404 Not Found response with an error message
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Create a new work time entry with period and activities
     */
    #[Route('', name: 'app_work_time_create', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Work time data with period and activities',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'consultantId',
                    type: 'integer',
                    example: 1,
                    description: 'Optional: Only for admins. If not provided, current user\'s ID will be used'
                ),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-06-01'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-06-30'),
                new OA\Property(property: 'totalHours', type: 'number', format: 'float', example: 160.0),
                new OA\Property(property: 'remoteWorkPercentage', type: 'number', format: 'float', example: 30.0),
                new OA\Property(property: 'notes', type: 'string', example: 'Work summary for the period'),
                new OA\Property(
                    property: 'activities',
                    type: 'array',
                    items: new OA\Items(
                        properties: [
                            new OA\Property(property: 'activityName', type: 'string', example: 'Development'),
                            new OA\Property(property: 'hours', type: 'number', format: 'float', example: 80.0),
                            new OA\Property(property: 'description', type: 'string', example: 'API development'),
                            new OA\Property(property: 'isBillable', type: 'boolean', example: true),
                            new OA\Property(property: 'clientId', type: 'integer', nullable: true, example: 1)
                        ]
                    )
                )
            ],
            required: ['startDate', 'endDate', 'totalHours']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Work time entry created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-06-01'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-06-30'),
                new OA\Property(property: 'totalHours', type: 'number', format: 'float', example: 160.0),
                new OA\Property(property: 'remoteWorkPercentage', type: 'number', format: 'float', example: 30.0),
                new OA\Property(property: 'notes', type: 'string', example: 'Work summary for the period'),
                new OA\Property(property: 'isValidated', type: 'boolean', example: false),
                new OA\Property(property: 'validationStatus', type: 'string', example: 'Pending validation'),
                new OA\Property(property: 'periodIdentifier', type: 'string', example: '2023-06-01 to 2023-06-30'),
                new OA\Property(property: 'durationInDays', type: 'integer', example: 30),
                new OA\Property(
                    property: 'activities',
                    type: 'array',
                    items: new OA\Items(
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'activityName', type: 'string', example: 'Development'),
                            new OA\Property(property: 'hours', type: 'number', format: 'float', example: 80.0),
                            new OA\Property(property: 'description', type: 'string', example: 'API development'),
                            new OA\Property(property: 'isBillable', type: 'boolean', example: true),
                            new OA\Property(property: 'percentageOfTotal', type: 'number', format: 'float', example: 50.0),
                            new OA\Property(
                                property: 'client',
                                type: 'object',
                                nullable: true,
                                properties: [
                                    new OA\Property(property: 'id', type: 'integer', example: 1),
                                    new OA\Property(property: 'name', type: 'string', example: 'Acme Corp'),
                                    new OA\Property(property: 'code', type: 'string', example: 'ACME')
                                ]
                            )
                        ]
                    )
                ),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid date format')
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You must be logged in as a consultant to create work time entries')
            ]
        )
    )]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $workTime = $this->workTimeService->createWorkTime($data);

            // Create enhanced response for period-based entry
            $response = $this->buildPeriodResponse($workTime);

            return $this->json($response, 201);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to create work time entry: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Update an existing work time entry
     */
    #[Route('/{id}', name: 'app_work_time_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Work time entry ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated work time data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'hours', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'remoteWork', type: 'boolean', example: true),
                new OA\Property(property: 'activity', type: 'string', example: 'Development'),
                new OA\Property(property: 'comment', type: 'string', example: 'Working on the API documentation')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Work time entry updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'hours', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'remoteWork', type: 'boolean', example: true),
                new OA\Property(property: 'activity', type: 'string', example: 'Development'),
                new OA\Property(property: 'comment', type: 'string', example: 'Working on API documentation'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'John'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Doe')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Work time entry not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Work time entry not found')
            ]
        )
    )]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $workTime = $this->workTimeService->updateWorkTime($id, $data);

            // Create enhanced response
            $response = $this->buildPeriodResponse($workTime);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Work time entry not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to update work time entry: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Delete a work time entry
     */
    #[Route('/{id}', name: 'app_work_time_delete', methods: ['DELETE'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Work time entry ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Work time entry deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'date', type: 'string', format: 'date', example: '2023-06-15'),
                new OA\Property(property: 'message', type: 'string', example: 'Work time entry successfully deleted'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Work time entry not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Work time entry not found')
            ]
        )
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            // Get the work time entry before deleting it to include in the response
            $workTime = $this->workTimeService->getWorkTimeById($id);

            // Store relevant information before deletion
            $response = [
                'id' => $workTime->getId(),
                'periodIdentifier' => $workTime->getPeriodIdentifier(),
                'message' => 'Work time entry successfully deleted',
                'success' => true
            ];

            // Delete the work time entry
            $this->workTimeService->deleteWorkTime($id);

            // Return a 200 OK response with information about the deleted entry
            return $this->json($response, 200);
        } catch (NotFoundHttpException $e) {
            // Return a 404 Not Found response with an error message
            return $this->json([
                'error' => 'Work time entry not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        }
    }

    /**
     * Get work time statistics for a consultant
     */
    #[Route('/statistics/{consultantId}', name: 'app_work_time_statistics', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'Consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns work time statistics',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'totalHours', type: 'number', format: 'float', example: 160.5),
                new OA\Property(property: 'remoteHours', type: 'number', format: 'float', example: 80.0),
                new OA\Property(property: 'remotePercentage', type: 'number', format: 'float', example: 49.8),
                new OA\Property(property: 'averageHoursPerDay', type: 'number', format: 'float', example: 7.5),
                new OA\Property(property: 'daysWorked', type: 'integer', example: 21),
                new OA\Property(
                    property: 'activitiesBreakdown',
                    type: 'object',
                    additionalProperties: new OA\AdditionalProperties(type: 'number')
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found')
            ]
        )
    )]
    public function statistics(int $consultantId, Request $request): JsonResponse
    {
        $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

        $statistics = $this->workTimeService->calculateStatistics($consultantId, $startDate, $endDate);
        return $this->json($statistics);
    }





    /**
     * Validate a work time entry (admin only)
     */
    #[Route('/{id}/validate', name: 'app_work_time_validate', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(name: 'id', description: 'Work time entry ID', in: 'path', required: true, schema: new OA\Schema(type: 'integer'))]
    #[OA\Response(
        response: 200,
        description: 'Work time entry validated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'isValidated', type: 'boolean', example: true),
                new OA\Property(property: 'validatedAt', type: 'string', format: 'datetime', example: '2023-06-15T10:30:00Z'),
                new OA\Property(
                    property: 'validatedBy',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 2),
                        new OA\Property(property: 'firstName', type: 'string', example: 'Admin'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'User')
                    ]
                ),
                new OA\Property(property: 'message', type: 'string', example: 'Work time entry validated successfully')
            ]
        )
    )]
    public function validate(int $id): JsonResponse
    {
        try {
            $workTime = $this->workTimeService->validateWorkTime($id);

            return $this->json([
                'id' => $workTime->getId(),
                'isValidated' => $workTime->isValidated(),
                'validatedAt' => $workTime->getValidatedAt()?->format('c'),
                'validatedBy' => $workTime->getValidatedBy() ? [
                    'id' => $workTime->getValidatedBy()->getId(),
                    'firstName' => $workTime->getValidatedBy()->getFirstName(),
                    'lastName' => $workTime->getValidatedBy()->getLastName()
                ] : null,
                'message' => 'Work time entry validated successfully'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        }
    }

    /**
     * Build response for work time entries
     */
    private function buildPeriodResponse(WorkTime $workTime): array
    {
        $response = [
            'id' => $workTime->getId(),
            'startDate' => $workTime->getStartDate()->format('Y-m-d'),
            'endDate' => $workTime->getEndDate()->format('Y-m-d'),
            'totalHours' => $workTime->getTotalHours(),
            'remoteWorkPercentage' => $workTime->getRemoteWorkPercentage(),
            'notes' => $workTime->getNotes(),
            'isValidated' => $workTime->isValidated(),
            'validationStatus' => $workTime->getValidationStatusText(),
            'periodIdentifier' => $workTime->getPeriodIdentifier(),
            'durationInDays' => $workTime->getPeriodDurationInDays(),
            'activities' => [],
            'consultant' => [
                'id' => $workTime->getConsultant()->getId(),
                'firstName' => $workTime->getConsultant()->getFirstName(),
                'lastName' => $workTime->getConsultant()->getLastName()
            ]
        ];

        // Add activities
        foreach ($workTime->getActivities() as $activity) {
            $response['activities'][] = [
                'id' => $activity->getId(),
                'activityName' => $activity->getActivityName(),
                'hours' => $activity->getHours(),
                'description' => $activity->getDescription(),
                'isBillable' => $activity->isBillable(),
                'percentageOfTotal' => $activity->getPercentageOfTotal(),
                'client' => $activity->getClient() ? [
                    'id' => $activity->getClient()->getId(),
                    'name' => $activity->getClient()->getName(),
                    'code' => $activity->getClient()->getCode()
                ] : null
            ];
        }

        // Add validation info if validated
        if ($workTime->isValidated()) {
            $response['validatedAt'] = $workTime->getValidatedAt()?->format('c');
            $response['validatedBy'] = $workTime->getValidatedBy() ? [
                'id' => $workTime->getValidatedBy()->getId(),
                'firstName' => $workTime->getValidatedBy()->getFirstName(),
                'lastName' => $workTime->getValidatedBy()->getLastName()
            ] : null;
        }

        return $response;
    }
}
