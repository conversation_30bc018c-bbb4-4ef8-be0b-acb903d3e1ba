<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Controller for API home page and documentation
 */
class ApiHomeController extends AbstractController
{
    /**
     * Home page - renders a simple HTML page with links to API documentation
     */
    #[Route('/', name: 'app_home')]
    public function home(): Response
    {
        return $this->render('home/index.html.twig');
    }

    /**
     * API home page - redirects to the home page
     */
    #[Route('/api/v1', name: 'app_api_home')]
    #[Route('/api/v1/', name: 'app_api_home_slash')]
    public function apiHome(): Response
    {
        return $this->redirect('/', Response::HTTP_MOVED_PERMANENTLY);
    }

    /**
     * API documentation shortcut - redirects to the full API documentation
     */
    #[Route('/api/doc', name: 'app_api_doc_redirect')]
    public function apiDocRedirect(): Response
    {
        return $this->redirect('/api/v1/doc', Response::HTTP_MOVED_PERMANENTLY);
    }
}
