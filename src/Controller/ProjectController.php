<?php

namespace App\Controller;

use App\Entity\Project;
use App\Service\ProjectService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use OpenApi\Attributes as OA;

#[Route('/api/v1/projects')]
#[OA\Tag(name: 'Projects')]
class ProjectController extends AbstractController
{
    public function __construct(
        private readonly ProjectService $projectService
    ) {}

    /**
     * Get all projects (admin only)
     */
    #[Route('', name: 'app_project_index', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'Returns all projects',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Refonte Site Web'),
                    new OA\Property(property: 'code', type: 'string', example: 'TECH-WEB-001'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Refonte complète du site web corporate'),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', nullable: true, example: '2024-01-15'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', nullable: true, example: '2024-06-30'),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true),
                    new OA\Property(
                        property: 'client',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'TechCorp Solutions'),
                            new OA\Property(property: 'code', type: 'string', example: 'TECH')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function index(): JsonResponse
    {
        $projects = $this->projectService->getAllProjects();

        $response = array_map(fn(Project $project) => $this->formatProjectResponse($project), $projects);

        return $this->json($response);
    }

    /**
     * Get active projects
     */
    #[Route('/active', name: 'app_project_active', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Returns all active projects',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Refonte Site Web'),
                    new OA\Property(property: 'code', type: 'string', example: 'TECH-WEB-001'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Refonte complète du site web corporate'),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', nullable: true, example: '2024-01-15'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', nullable: true, example: '2024-06-30'),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true),
                    new OA\Property(
                        property: 'client',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'TechCorp Solutions'),
                            new OA\Property(property: 'code', type: 'string', example: 'TECH')
                        ]
                    )
                ]
            )
        )
    )]
    public function active(): JsonResponse
    {
        $projects = $this->projectService->getActiveProjects();

        $response = array_map(fn(Project $project) => $this->formatProjectResponse($project), $projects);

        return $this->json($response);
    }

    /**
     * Get projects by client
     */
    #[Route('/client/{clientId}', name: 'app_project_by_client', methods: ['GET'])]
    #[OA\Parameter(
        name: 'clientId',
        description: 'Client ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'active',
        description: 'Filter by active status (true/false)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns projects for the client',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'name', type: 'string', example: 'Refonte Site Web'),
                    new OA\Property(property: 'code', type: 'string', example: 'TECH-WEB-001'),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Refonte complète du site web corporate'),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', nullable: true, example: '2024-01-15'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', nullable: true, example: '2024-06-30'),
                    new OA\Property(property: 'isActive', type: 'boolean', example: true)
                ]
            )
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Client not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Client not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function byClient(int $clientId, Request $request): JsonResponse
    {
        try {
            $activeOnly = $request->query->get('active');
            $activeOnly = $activeOnly !== null ? filter_var($activeOnly, FILTER_VALIDATE_BOOLEAN) : true;

            $projects = $this->projectService->getProjectsByClient($clientId, $activeOnly);

            $response = array_map(fn(Project $project) => $this->formatProjectResponse($project, false), $projects);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Client not found',
                'success' => false
            ], 404);
        }
    }

    /**
     * Get a specific project by ID
     */
    #[Route('/{id}', name: 'app_project_show', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Project ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the project details',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Refonte Site Web'),
                new OA\Property(property: 'code', type: 'string', example: 'TECH-WEB-001'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Refonte complète du site web corporate'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', nullable: true, example: '2024-01-15'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', nullable: true, example: '2024-06-30'),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(
                    property: 'client',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'TechCorp Solutions'),
                        new OA\Property(property: 'code', type: 'string', example: 'TECH')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Project not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Project not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function show(int $id): JsonResponse
    {
        try {
            $project = $this->projectService->getProjectById($id);

            $response = $this->formatProjectResponse($project);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Project not found',
                'success' => false
            ], 404);
        }
    }

    /**
     * Format project response
     */
    private function formatProjectResponse(Project $project, bool $includeClient = true): array
    {
        $response = [
            'id' => $project->getId(),
            'name' => $project->getName(),
            'code' => $project->getCode(),
            'description' => $project->getDescription(),
            'startDate' => $project->getStartDate()?->format('Y-m-d'),
            'endDate' => $project->getEndDate()?->format('Y-m-d'),
            'isActive' => $project->isActive()
        ];

        if ($includeClient && $project->getClient()) {
            $response['client'] = [
                'id' => $project->getClient()->getId(),
                'name' => $project->getClient()->getName(),
                'code' => $project->getClient()->getCode()
            ];
        }

        return $response;
    }

    /**
     * Create a new project (admin only)
     */
    #[Route('', name: 'app_project_create', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\RequestBody(
        description: 'Project data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Refonte Site Web'),
                new OA\Property(property: 'code', type: 'string', example: 'TECH-WEB-001'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Refonte complète du site web corporate'),
                new OA\Property(property: 'clientId', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', nullable: true, example: '2024-01-15'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', nullable: true, example: '2024-06-30'),
                new OA\Property(property: 'isActive', type: 'boolean', example: true)
            ],
            required: ['name', 'code', 'clientId']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Project created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Refonte Site Web'),
                new OA\Property(property: 'code', type: 'string', example: 'TECH-WEB-001'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Refonte complète du site web corporate'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', nullable: true, example: '2024-01-15'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', nullable: true, example: '2024-06-30'),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(
                    property: 'client',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'TechCorp Solutions'),
                        new OA\Property(property: 'code', type: 'string', example: 'TECH')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Project name is required'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    #[OA\Response(
        response: 404,
        description: 'Client not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Client not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $project = $this->projectService->createProject($data);

            $response = $this->formatProjectResponse($project);

            return $this->json($response, 201);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Client not found',
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }
}
