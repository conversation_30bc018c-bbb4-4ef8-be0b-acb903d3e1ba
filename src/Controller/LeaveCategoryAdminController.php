<?php

namespace App\Controller;

use App\Entity\LeaveCategory;
use App\Repository\LeaveCategoryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use OpenApi\Attributes as OA;

/**
 * Admin-only operations for leave categories
 */
#[Route('/api/v1/admin/leave-categories')]
#[IsGranted('ROLE_ADMIN')]
#[OA\Tag(name: 'Leave Categories Admin')]
class LeaveCategoryAdminController extends AbstractController
{
    public function __construct(
        private readonly LeaveCategoryRepository $leaveCategoryRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    /**
     * Update a leave category
     */
    #[Route('/{id}', name: 'app_leave_category_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated leave category data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave category updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Congés payés annuels'),
                new OA\Property(property: 'requiresDescription', type: 'boolean', example: false),
                new OA\Property(property: 'isActive', type: 'boolean', example: true),
                new OA\Property(property: 'isPaid', type: 'boolean', example: true),
                new OA\Property(property: 'sortOrder', type: 'integer', example: 1)
            ]
        )
    )]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $category = $this->leaveCategoryRepository->find($id);

            if (!$category) {
                throw new NotFoundHttpException('Leave category not found');
            }

            $data = json_decode($request->getContent(), true);

            // Check if code already exists (excluding current category)
            if (isset($data['code']) && $data['code'] !== $category->getCode()) {
                $existingCategory = $this->leaveCategoryRepository->findByCode($data['code']);
                if ($existingCategory) {
                    return $this->json([
                        'error' => 'Category code already exists',
                        'success' => false
                    ], 400);
                }
            }

            // Update fields
            if (isset($data['name'])) {
                $category->setName($data['name']);
            }
            
            if (isset($data['code'])) {
                $category->setCode($data['code']);
            }
            
            if (isset($data['description'])) {
                $category->setDescription($data['description']);
            }
            
            if (isset($data['requiresDescription'])) {
                $category->setRequiresDescription($data['requiresDescription']);
            }
            
            if (isset($data['isActive'])) {
                $category->setIsActive($data['isActive']);
            }
            
            if (isset($data['isPaid'])) {
                $category->setIsPaid($data['isPaid']);
            }
            
            if (isset($data['sortOrder'])) {
                $category->setSortOrder($data['sortOrder']);
            }

            $this->entityManager->flush();

            $response = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'code' => $category->getCode(),
                'description' => $category->getDescription(),
                'requiresDescription' => $category->isRequiresDescription(),
                'isActive' => $category->isActive(),
                'isPaid' => $category->isPaid(),
                'sortOrder' => $category->getSortOrder()
            ];

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave category not found',
                'success' => false
            ], 404);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to update leave category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Delete a leave category
     */
    #[Route('/{id}', name: 'app_leave_category_delete', methods: ['DELETE'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave category ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave category deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Leave category deleted successfully'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Cannot delete category with existing leaves',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Cannot delete category with existing leaves'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            $category = $this->leaveCategoryRepository->find($id);

            if (!$category) {
                throw new NotFoundHttpException('Leave category not found');
            }

            // Check if category has associated leaves
            if (!$category->getLeaves()->isEmpty()) {
                return $this->json([
                    'error' => 'Cannot delete category with existing leaves',
                    'success' => false
                ], 400);
            }

            $this->entityManager->remove($category);
            $this->entityManager->flush();

            return $this->json([
                'message' => 'Leave category deleted successfully',
                'success' => true
            ]);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave category not found',
                'success' => false
            ], 404);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Failed to delete leave category: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
