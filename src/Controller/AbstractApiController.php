<?php

namespace App\Controller;

use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Base controller for API endpoints with common OpenAPI annotations
 */
#[OA\Response(
    response: 401,
    description: 'Unauthorized - JWT token is missing or invalid',
    content: new OA\JsonContent(
        properties: [
            new OA\Property(property: 'error', type: 'string', example: 'JWT token is missing or invalid')
        ]
    )
)]
#[OA\Response(
    response: 403,
    description: 'Forbidden - Insufficient permissions',
    content: new OA\JsonContent(
        properties: [
            new OA\Property(property: 'error', type: 'string', example: 'Access denied')
        ]
    )
)]
#[OA\Response(
    response: 404,
    description: 'Not Found - Resource not found',
    content: new OA\JsonContent(
        properties: [
            new OA\Property(property: 'error', type: 'string', example: 'Resource not found')
        ]
    )
)]
#[OA\Response(
    response: 400,
    description: 'Bad Request - Invalid input data',
    content: new OA\JsonContent(
        properties: [
            new OA\Property(property: 'error', type: 'string', example: 'Invalid input data')
        ]
    )
)]
#[OA\Response(
    response: 500,
    description: 'Internal Server Error',
    content: new OA\JsonContent(
        properties: [
            new OA\Property(property: 'error', type: 'string', example: 'An unexpected error occurred')
        ]
    )
)]
abstract class AbstractApiController extends AbstractController
{
    /**
     * Create a standardized error response
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code
     * @param array<string, mixed> $additionalData Additional data to include in the response
     */
    protected function createErrorResponse(string $message, int $statusCode, array $additionalData = []): JsonResponse
    {
        $data = array_merge(['error' => $message], $additionalData);
        return $this->json($data, $statusCode);
    }

    /**
     * Create a standardized success response
     *
     * @param array<string, mixed> $data Response data
     * @param int $statusCode HTTP status code
     */
    protected function createSuccessResponse(array $data, int $statusCode = 200): JsonResponse
    {
        return $this->json($data, $statusCode);
    }
}
