<?php

namespace App\Controller;

use App\Entity\Consultant;
use App\Entity\User;
use App\Repository\ConsultantRepository;
use App\Service\AuthService;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

/**
 * Controller for authentication endpoints
 */
#[Route('/api/v1/auth')]
#[OA\Tag(name: 'Authentication')]
class AuthController extends AbstractApiController
{
    public function __construct(
        private readonly AuthService $authService,
        private readonly JWTTokenManagerInterface $jwtManager,
        private readonly UserPasswordHasherInterface $passwordHasher,
        private readonly ConsultantRepository $consultantRepository
    ) {}

    /**
     * Login to get a JWT token
     */
    #[Route('/login', name: 'api_login', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Login credentials',
        required: true,
        content: new OA\JsonContent(ref: '#/components/schemas/LoginRequest')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns JWT token and user information',
        content: new OA\JsonContent(ref: '#/components/schemas/LoginResponse')
    )]
    #[OA\Response(
        response: 401,
        description: 'Invalid credentials',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Invalid credentials')
            ]
        )
    )]
    public function login(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!isset($data['email']) || !isset($data['password'])) {
            return $this->createErrorResponse('Missing email or password', Response::HTTP_BAD_REQUEST);
        }

        $consultant = $this->consultantRepository->findOneBy(['email' => $data['email']]);

        if (!$consultant || !$this->passwordHasher->isPasswordValid($consultant, $data['password'])) {
            return $this->createErrorResponse('Invalid credentials', Response::HTTP_UNAUTHORIZED);
        }

        $token = $this->jwtManager->create($consultant);

        return $this->json([
            'token' => $token,
            'user' => [
                'id' => $consultant->getId(),
                'email' => $consultant->getEmail(),
                'firstName' => $consultant->getFirstName(),
                'lastName' => $consultant->getLastName(),
                'roles' => $consultant->getRoles(),
                'isAdmin' => $consultant->isAdmin(),
            ]
        ]);
    }

    /**
     * Register a new user
     */
    #[Route('/register', name: 'api_register', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Registration data',
        required: true,
        content: new OA\JsonContent(ref: '#/components/schemas/RegistrationRequest')
    )]
    #[OA\Response(
        response: 201,
        description: 'User registered successfully',
        content: new OA\JsonContent(ref: '#/components/schemas/RegistrationResponse')
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Email already in use')
            ]
        )
    )]
    public function register(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        // Validate required fields
        $requiredFields = ['email', 'password', 'firstName', 'lastName'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return $this->createErrorResponse(
                'Missing required fields: ' . implode(', ', $missingFields),
                Response::HTTP_BAD_REQUEST
            );
        }

        // Check if email is already in use
        $existingUser = $this->consultantRepository->findOneBy(['email' => $data['email']]);
        if ($existingUser) {
            return $this->createErrorResponse('Email already in use', Response::HTTP_BAD_REQUEST);
        }

        try {
            // Create new consultant
            $consultant = new Consultant();
            $consultant->setEmail($data['email']);
            $consultant->setPassword($this->passwordHasher->hashPassword($consultant, $data['password']));
            $consultant->setFirstName($data['firstName']);
            $consultant->setLastName($data['lastName']);

            if (isset($data['phone'])) {
                $consultant->setPhone($data['phone']);
            }

            $consultant->setIsAdmin(false);
            $consultant->setRoles(['ROLE_USER']);

            // Use EntityManager instead of repository save method
            $entityManager = $this->consultantRepository->getEntityManager();
            $entityManager->persist($consultant);
            $entityManager->flush();

            return $this->json([
                'message' => 'User registered successfully',
                'user' => [
                    'id' => $consultant->getId(),
                    'email' => $consultant->getEmail(),
                    'firstName' => $consultant->getFirstName(),
                    'lastName' => $consultant->getLastName(),
                ]
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->createErrorResponse('Registration failed: ' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
