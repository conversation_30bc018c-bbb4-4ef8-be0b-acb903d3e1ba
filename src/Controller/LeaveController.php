<?php

namespace App\Controller;

use App\Entity\Leave;
use App\Service\LeaveService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use OpenApi\Attributes as OA;

/**
 * Controller for managing leave requests
 */
#[Route('/api/v1/leaves')]
#[OA\Tag(name: 'Leaves')]
class LeaveController extends AbstractController
{
    public function __construct(
        private readonly LeaveService $leaveService
    ) {}

    /**
     * Format a leave entity for API response
     */
    private function formatLeaveResponse(Leave $leave, bool $includeConsultant = true): array
    {
        $response = [
            'id' => $leave->getId(),
            'startDate' => $leave->getStartDate()->format('Y-m-d'),
            'endDate' => $leave->getEndDate()->format('Y-m-d'),
            'numberOfDays' => $leave->getNumberOfDays(),
            'category' => [
                'id' => $leave->getCategory()->getId(),
                'name' => $leave->getCategory()->getName(),
                'code' => $leave->getCategory()->getCode(),
                'isPaid' => $leave->getCategory()->isPaid()
            ],
            'description' => $leave->getDescription(),
            'status' => $leave->getStatus()
        ];

        if ($includeConsultant) {
            $response['consultant'] = [
                'id' => $leave->getConsultant()->getId(),
                'firstName' => $leave->getConsultant()->getFirstName(),
                'lastName' => $leave->getConsultant()->getLastName()
            ];
        }

        return $response;
    }

    /**
     * Get all leave requests (admin only)
     */
    #[Route('', name: 'app_leave_index', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'Returns all leave requests',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                    new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                    new OA\Property(
                        property: 'category',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                            new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                            new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                        ]
                    ),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                    new OA\Property(property: 'status', type: 'string', example: 'approved'),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function index(): JsonResponse
    {
        $leaves = $this->leaveService->getAllLeaves();

        // Format the response
        $response = array_map(fn(Leave $leave) => $this->formatLeaveResponse($leave), $leaves);

        return $this->json($response);
    }

    /**
     * Get all pending leave requests (admin only)
     */
    #[Route('/pending', name: 'app_leave_pending', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Response(
        response: 200,
        description: 'Returns all pending leave requests',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                    new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                    new OA\Property(
                        property: 'category',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                            new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                            new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                        ]
                    ),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                    new OA\Property(property: 'status', type: 'string', example: 'approved'),
                    new OA\Property(
                        property: 'consultant',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                            new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                        ]
                    )
                ]
            )
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function pending(): JsonResponse
    {
        $leaves = $this->leaveService->getPendingLeaves();

        // Format the response
        $response = array_map(fn(Leave $leave) => $this->formatLeaveResponse($leave), $leaves);

        return $this->json($response);
    }

    /**
     * Get a specific leave request by ID
     */
    #[Route('/{id}', name: 'app_leave_show', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave request ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the leave request details',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(
                    property: 'category',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                        new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                        new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                    ]
                ),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                new OA\Property(property: 'status', type: 'string', example: 'approved'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Leave request not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Leave request not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The leave request with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function show(int $id): JsonResponse
    {
        try {
            $leave = $this->leaveService->getLeaveById($id);

            // Format the response
            $response = $this->formatLeaveResponse($leave);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave request not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Get leave requests for a specific consultant
     */
    #[Route('/consultant/{consultantId}', name: 'app_leave_by_consultant', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'Consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Parameter(
        name: 'start',
        description: 'Start date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Parameter(
        name: 'end',
        description: 'End date (format: Y-m-d)',
        in: 'query',
        required: false,
        schema: new OA\Schema(type: 'string', format: 'date')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns leave requests for the consultant',
        content: new OA\JsonContent(
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                    new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                    new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                    new OA\Property(
                        property: 'category',
                        type: 'object',
                        properties: [
                            new OA\Property(property: 'id', type: 'integer', example: 1),
                            new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                            new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                            new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                        ]
                    ),
                    new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                    new OA\Property(property: 'status', type: 'string', example: 'approved')
                ]
            )
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The consultant with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You can only access your own data'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function byConsultant(int $consultantId, Request $request): JsonResponse
    {
        try {
            $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
            $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

            $leaves = $this->leaveService->getLeavesByConsultant($consultantId, $startDate, $endDate);

            // Format the response
            $response = array_map(fn(Leave $leave) => $this->formatLeaveResponse($leave, false), $leaves);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Create a new leave request
     */
    #[Route('', name: 'app_leave_create', methods: ['POST'])]
    #[OA\RequestBody(
        description: 'Leave request data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'consultantId', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(property: 'categoryId', type: 'integer', example: 1, description: 'Leave category ID'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été')
            ],
            required: ['consultantId', 'startDate', 'endDate', 'numberOfDays', 'categoryId']
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Leave request created successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(
                    property: 'category',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                        new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                        new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                    ]
                ),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                new OA\Property(property: 'status', type: 'string', example: 'approved'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Date de début et date de fin sont requises'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant non trouvé'),
                new OA\Property(property: 'message', type: 'string', example: 'Le consultant avec l\'ID 999 n\'a pas été trouvé'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You can only access your own data'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function create(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $leave = $this->leaveService->createLeave($data);

            // Format the response
            $response = $this->formatLeaveResponse($leave);

            return $this->json($response, 201);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Update an existing leave request
     */
    #[Route('/{id}', name: 'app_leave_update', methods: ['PUT'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave request ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Updated leave request data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(property: 'categoryId', type: 'integer', example: 1, description: 'Leave category ID'),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave request updated successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(
                    property: 'category',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                        new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                        new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                    ]
                ),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                new OA\Property(property: 'status', type: 'string', example: 'approved'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid input data',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Not enough leave days available'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Leave request not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Leave request not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The leave request with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You cannot modify a leave that is not pending'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $leave = $this->leaveService->updateLeave($id, $data);

            // Format the response
            $response = $this->formatLeaveResponse($leave);

            return $this->json($response);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave request not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Delete a leave request
     */
    #[Route('/{id}', name: 'app_leave_delete', methods: ['DELETE'])]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave request ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave request deleted successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'message', type: 'string', example: 'Leave request successfully deleted'),
                new OA\Property(property: 'success', type: 'boolean', example: true)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Leave request not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Leave request not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The leave request with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You cannot delete a leave that is not pending'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function delete(int $id): JsonResponse
    {
        try {
            // Get the leave request before deleting it to include in the response
            $leave = $this->leaveService->getLeaveById($id);

            // Store relevant information before deletion
            $response = [
                'id' => $leave->getId(),
                'startDate' => $leave->getStartDate()->format('Y-m-d'),
                'message' => 'Leave request successfully deleted',
                'success' => true
            ];

            // Delete the leave request
            $this->leaveService->deleteLeave($id);

            // Return a 200 OK response with information about the deleted entry
            return $this->json($response, 200);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave request not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Approve a leave request (admin only)
     */
    #[Route('/{id}/approve', name: 'app_leave_approve', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave request ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave request approved successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(
                    property: 'category',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                        new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                        new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                    ]
                ),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été'),
                new OA\Property(property: 'status', type: 'string', example: 'approved'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Leave request not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Leave request not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The leave request with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid operation',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Only pending leaves can be approved'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function approve(int $id): JsonResponse
    {
        try {
            $leave = $this->leaveService->approveLeave($id);

            // Format the response
            $response = $this->formatLeaveResponse($leave);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave request not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Reject a leave request (admin only)
     */
    #[Route('/{id}/reject', name: 'app_leave_reject', methods: ['PUT'])]
    #[IsGranted('ROLE_ADMIN')]
    #[OA\Parameter(
        name: 'id',
        description: 'Leave request ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\RequestBody(
        description: 'Rejection reason',
        required: false,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'reason', type: 'string', example: 'Contraintes de ressources')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Leave request rejected successfully',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'id', type: 'integer', example: 1),
                new OA\Property(property: 'startDate', type: 'string', format: 'date', example: '2023-07-10'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date', example: '2023-07-15'),
                new OA\Property(property: 'numberOfDays', type: 'number', format: 'float', example: 5.0),
                new OA\Property(
                    property: 'category',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'name', type: 'string', example: 'Congés annuels'),
                        new OA\Property(property: 'code', type: 'string', example: 'ANNUAL'),
                        new OA\Property(property: 'isPaid', type: 'boolean', example: true)
                    ]
                ),
                new OA\Property(property: 'description', type: 'string', nullable: true, example: 'Vacances d\'été\n\nMotif de refus: Contraintes de ressources'),
                new OA\Property(property: 'status', type: 'string', example: 'rejected'),
                new OA\Property(
                    property: 'consultant',
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 1),
                        new OA\Property(property: 'firstName', type: 'string', example: 'Marie'),
                        new OA\Property(property: 'lastName', type: 'string', example: 'Dubois')
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Leave request not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Leave request not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The leave request with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Invalid operation',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Only pending leaves can be rejected'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied - Admin role required'
    )]
    public function reject(int $id, Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $reason = $data['reason'] ?? null;

            $leave = $this->leaveService->rejectLeave($id, $reason);

            // Format the response
            $response = $this->formatLeaveResponse($leave);

            return $this->json($response);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Leave request not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (\InvalidArgumentException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 400);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        }
    }

    /**
     * Calculate the remaining leave balance for a consultant
     */
    #[Route('/balance/{consultantId}', name: 'app_leave_balance', methods: ['GET'])]
    #[OA\Parameter(
        name: 'consultantId',
        description: 'Consultant ID',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'integer')
    )]
    #[OA\Response(
        response: 200,
        description: 'Returns the remaining leave balance',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'balance', type: 'number', format: 'float', example: 15.5)
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Consultant not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'Consultant not found'),
                new OA\Property(property: 'message', type: 'string', example: 'The consultant with ID 999 was not found'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Access denied',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'error', type: 'string', example: 'You can only access your own data'),
                new OA\Property(property: 'success', type: 'boolean', example: false)
            ]
        )
    )]
    public function balance(int $consultantId): JsonResponse
    {
        try {
            $balance = $this->leaveService->calculateLeaveBalance($consultantId);
            return $this->json(['balance' => $balance]);
        } catch (NotFoundHttpException $e) {
            return $this->json([
                'error' => 'Consultant not found',
                'message' => $e->getMessage(),
                'success' => false
            ], 404);
        } catch (AccessDeniedException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 403);
        } catch (\RuntimeException $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
