<?php

namespace App\Entity;

use App\Repository\WorkTimeActivityRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: WorkTimeActivityRepository::class)]
class WorkTimeActivity
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: WorkTime::class, inversedBy: 'activities')]
    #[ORM\JoinColumn(nullable: false)]
    private ?WorkTime $workTime = null;



    #[ORM\Column(length: 255)]
    private ?string $activityName = null;

    #[ORM\Column(type: 'float')]
    private ?float $hours = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'boolean')]
    private ?bool $isBillable = false;

    #[ORM\ManyToOne(targetEntity: Project::class, inversedBy: 'workTimeActivities')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Project $project = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getWorkTime(): ?WorkTime
    {
        return $this->workTime;
    }

    public function setWorkTime(?WorkTime $workTime): static
    {
        $this->workTime = $workTime;
        return $this;
    }



    public function getActivityName(): ?string
    {
        return $this->activityName;
    }

    public function setActivityName(string $activityName): static
    {
        $this->activityName = $activityName;
        return $this;
    }

    public function getHours(): ?float
    {
        return $this->hours;
    }

    public function setHours(float $hours): static
    {
        $this->hours = $hours;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function isBillable(): ?bool
    {
        return $this->isBillable;
    }

    public function setIsBillable(bool $isBillable): static
    {
        $this->isBillable = $isBillable;
        return $this;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): static
    {
        $this->project = $project;
        return $this;
    }

    /**
     * Get a display name for the activity including client and project if applicable
     */
    public function getDisplayName(): string
    {
        $parts = [];

        if ($this->workTime && $this->workTime->getClient()) {
            $parts[] = $this->workTime->getClient()->getName();
        }

        if ($this->project) {
            $parts[] = $this->project->getName();
        }

        $parts[] = $this->activityName;

        return implode(' - ', $parts);
    }

    /**
     * Calculate percentage of total work time for this activity
     */
    public function getPercentageOfTotal(): float
    {
        if (!$this->workTime || $this->workTime->getTotalHours() <= 0) {
            return 0.0;
        }

        return ($this->hours / $this->workTime->getTotalHours()) * 100.0;
    }
}
