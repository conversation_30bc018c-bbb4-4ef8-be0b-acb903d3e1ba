<?php

namespace App\Entity;

use App\Repository\CompanyRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CompanyRepository::class)]
class Company
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $address = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $phone = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $email = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $website = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $siret = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $annualLeaveDays = 25;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $workHoursPerDay = 7.5;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $maxRemoteWorkPercentage = 50.0;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $defaultRemoteWorkPercentage = 20.0;

    #[ORM\Column(type: 'boolean')]
    private ?bool $workTimeValidationRequired = false;

    #[ORM\Column(type: 'boolean')]
    private ?bool $holidayPaymentEnabled = true;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): static
    {
        $this->address = $address;
        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): static
    {
        $this->phone = $phone;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): static
    {
        $this->email = $email;
        return $this;
    }

    public function getWebsite(): ?string
    {
        return $this->website;
    }

    public function setWebsite(?string $website): static
    {
        $this->website = $website;
        return $this;
    }

    public function getSiret(): ?string
    {
        return $this->siret;
    }

    public function setSiret(?string $siret): static
    {
        $this->siret = $siret;
        return $this;
    }

    public function getAnnualLeaveDays(): ?int
    {
        return $this->annualLeaveDays;
    }

    public function setAnnualLeaveDays(?int $annualLeaveDays): static
    {
        $this->annualLeaveDays = $annualLeaveDays;
        return $this;
    }

    public function getWorkHoursPerDay(): ?float
    {
        return $this->workHoursPerDay;
    }

    public function setWorkHoursPerDay(?float $workHoursPerDay): static
    {
        $this->workHoursPerDay = $workHoursPerDay;
        return $this;
    }

    public function getMaxRemoteWorkPercentage(): ?float
    {
        return $this->maxRemoteWorkPercentage;
    }

    public function setMaxRemoteWorkPercentage(?float $maxRemoteWorkPercentage): static
    {
        $this->maxRemoteWorkPercentage = $maxRemoteWorkPercentage;
        return $this;
    }

    /**
     * Check if remote work is unlimited (no percentage limit)
     * Returns true if maxRemoteWorkPercentage is null or >= 100
     */
    public function isRemoteWorkUnlimited(): bool
    {
        return $this->maxRemoteWorkPercentage === null || $this->maxRemoteWorkPercentage >= 100.0;
    }

    public function getDefaultRemoteWorkPercentage(): ?float
    {
        return $this->defaultRemoteWorkPercentage;
    }

    public function setDefaultRemoteWorkPercentage(?float $defaultRemoteWorkPercentage): static
    {
        $this->defaultRemoteWorkPercentage = $defaultRemoteWorkPercentage;
        return $this;
    }

    public function isWorkTimeValidationRequired(): ?bool
    {
        return $this->workTimeValidationRequired;
    }

    public function setWorkTimeValidationRequired(?bool $workTimeValidationRequired): static
    {
        $this->workTimeValidationRequired = $workTimeValidationRequired;
        return $this;
    }

    public function isHolidayPaymentEnabled(): ?bool
    {
        return $this->holidayPaymentEnabled;
    }

    public function setHolidayPaymentEnabled(?bool $holidayPaymentEnabled): static
    {
        $this->holidayPaymentEnabled = $holidayPaymentEnabled;
        return $this;
    }
}
