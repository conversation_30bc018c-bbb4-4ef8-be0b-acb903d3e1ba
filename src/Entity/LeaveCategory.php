<?php

namespace App\Entity;

use App\Repository\LeaveCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LeaveCategoryRepository::class)]
class LeaveCategory
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 50, unique: true)]
    private ?string $code = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column]
    private ?bool $requiresDescription = false;

    #[ORM\Column]
    private ?bool $isActive = true;

    #[ORM\Column]
    private ?bool $isPaid = true;

    #[ORM\Column]
    private ?int $sortOrder = 0;

    #[ORM\OneToMany(mappedBy: 'category', targetEntity: Leave::class)]
    private Collection $leaves;

    public function __construct()
    {
        $this->leaves = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function isRequiresDescription(): ?bool
    {
        return $this->requiresDescription;
    }

    public function setRequiresDescription(bool $requiresDescription): static
    {
        $this->requiresDescription = $requiresDescription;
        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        return $this;
    }

    public function isPaid(): ?bool
    {
        return $this->isPaid;
    }

    public function setIsPaid(bool $isPaid): static
    {
        $this->isPaid = $isPaid;
        return $this;
    }

    public function getSortOrder(): ?int
    {
        return $this->sortOrder;
    }

    public function setSortOrder(int $sortOrder): static
    {
        $this->sortOrder = $sortOrder;
        return $this;
    }

    /**
     * @return Collection<int, Leave>
     */
    public function getLeaves(): Collection
    {
        return $this->leaves;
    }

    public function addLeave(Leave $leave): static
    {
        if (!$this->leaves->contains($leave)) {
            $this->leaves->add($leave);
            $leave->setCategory($this);
        }

        return $this;
    }

    public function removeLeave(Leave $leave): static
    {
        if ($this->leaves->removeElement($leave)) {
            // set the owning side to null (unless already changed)
            if ($leave->getCategory() === $this) {
                $leave->setCategory(null);
            }
        }

        return $this;
    }
}
