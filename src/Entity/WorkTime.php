<?php

namespace App\Entity;

use App\Repository\WorkTimeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use DateTimeInterface;

#[ORM\Entity(repositoryClass: WorkTimeRepository::class)]
class WorkTime
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Consultant::class, inversedBy: 'workTimes')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Consultant $consultant = null;

    #[ORM\Column(type: 'date')]
    private ?DateTimeInterface $startDate = null;

    #[ORM\Column(type: 'date')]
    private ?DateTimeInterface $endDate = null;

    #[ORM\Column(type: 'float')]
    private ?float $totalHours = null;

    #[ORM\Column(type: 'float')]
    private ?float $remoteWorkPercentage = 0.0;

    #[ORM\Column(type: 'boolean')]
    private ?bool $isValidated = false;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTimeInterface $validatedAt = null;

    #[ORM\ManyToOne(targetEntity: Consultant::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Consultant $validatedBy = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $notes = null;

    #[ORM\Column(type: 'datetime')]
    private ?DateTimeInterface $createdAt = null;

    #[ORM\Column(type: 'datetime')]
    private ?DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(mappedBy: 'workTime', targetEntity: WorkTimeActivity::class, cascade: ['persist', 'remove'])]
    private Collection $activities;

    public function __construct()
    {
        $this->activities = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getConsultant(): ?Consultant
    {
        return $this->consultant;
    }

    public function setConsultant(?Consultant $consultant): static
    {
        $this->consultant = $consultant;
        return $this;
    }

    public function getStartDate(): ?DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(DateTimeInterface $startDate): static
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getEndDate(): ?DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(DateTimeInterface $endDate): static
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function getTotalHours(): ?float
    {
        return $this->totalHours;
    }

    public function setTotalHours(float $totalHours): static
    {
        $this->totalHours = $totalHours;
        return $this;
    }

    public function getRemoteWorkPercentage(): ?float
    {
        return $this->remoteWorkPercentage;
    }

    public function setRemoteWorkPercentage(float $remoteWorkPercentage): static
    {
        $this->remoteWorkPercentage = $remoteWorkPercentage;
        return $this;
    }

    public function isValidated(): ?bool
    {
        return $this->isValidated;
    }

    public function setIsValidated(bool $isValidated): static
    {
        $this->isValidated = $isValidated;
        return $this;
    }

    public function getValidatedAt(): ?DateTimeInterface
    {
        return $this->validatedAt;
    }

    public function setValidatedAt(?DateTimeInterface $validatedAt): static
    {
        $this->validatedAt = $validatedAt;
        return $this;
    }

    public function getValidatedBy(): ?Consultant
    {
        return $this->validatedBy;
    }

    public function setValidatedBy(?Consultant $validatedBy): static
    {
        $this->validatedBy = $validatedBy;
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;
        return $this;
    }

    public function getCreatedAt(): ?DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(DateTimeInterface $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * @return Collection<int, WorkTimeActivity>
     */
    public function getActivities(): Collection
    {
        return $this->activities;
    }

    public function addActivity(WorkTimeActivity $activity): static
    {
        if (!$this->activities->contains($activity)) {
            $this->activities->add($activity);
            $activity->setWorkTime($this);
        }

        return $this;
    }

    public function removeActivity(WorkTimeActivity $activity): static
    {
        if ($this->activities->removeElement($activity)) {
            if ($activity->getWorkTime() === $this) {
                $activity->setWorkTime(null);
            }
        }

        return $this;
    }

    // Utility methods
    /**
     * Get the period duration in days
     */
    public function getPeriodDurationInDays(): int
    {
        if (!$this->startDate || !$this->endDate) {
            return 0;
        }

        return $this->startDate->diff($this->endDate)->days + 1;
    }

    /**
     * Get period identifier (YYYY-MM-DD to YYYY-MM-DD format)
     */
    public function getPeriodIdentifier(): string
    {
        if ($this->startDate && $this->endDate) {
            return $this->startDate->format('Y-m-d') . ' to ' . $this->endDate->format('Y-m-d');
        }

        return 'Unknown period';
    }

    /**
     * Calculate total hours from activities
     */
    public function calculateTotalHoursFromActivities(): float
    {
        $total = 0.0;
        foreach ($this->activities as $activity) {
            $total += $activity->getHours();
        }
        return $total;
    }

    /**
     * Update the updatedAt timestamp
     */
    public function updateTimestamp(): void
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Check if activities total matches declared total hours
     */
    public function hasValidActivityHours(): bool
    {
        if ($this->activities->isEmpty()) {
            return true; // No activities to validate
        }

        $calculatedTotal = $this->calculateTotalHoursFromActivities();
        $declaredTotal = $this->totalHours ?? 0.0;

        return abs($calculatedTotal - $declaredTotal) < 0.01;
    }

    /**
     * Get validation status text
     */
    public function getValidationStatusText(): string
    {
        if ($this->isValidated) {
            return 'Validated';
        }

        return 'Pending validation';
    }
}
