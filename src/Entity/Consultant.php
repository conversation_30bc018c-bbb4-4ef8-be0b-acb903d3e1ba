<?php

namespace App\Entity;

use App\Repository\ConsultantRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ConsultantRepository::class)]
class Consultant extends User
{
    #[ORM\Column(length: 255)]
    private ?string $lastName = null;

    #[ORM\Column(length: 255)]
    private ?string $firstName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $phone = null;

    #[ORM\Column]
    private ?bool $isAdmin = false;

    #[ORM\OneToMany(mappedBy: 'consultant', targetEntity: WorkTime::class, cascade: ['remove'])]
    private Collection $workTimes;

    #[ORM\OneToMany(mappedBy: 'consultant', targetEntity: Expense::class, cascade: ['remove'])]
    private Collection $expenses;

    #[ORM\OneToMany(mappedBy: 'consultant', targetEntity: Leave::class, cascade: ['remove'])]
    private Collection $leaves;

    public function __construct()
    {
        parent::__construct();
        $this->workTimes = new ArrayCollection();
        $this->expenses = new ArrayCollection();
        $this->leaves = new ArrayCollection();
    }

    /**
     * @return array<string>
     */
    public function getRoles(): array
    {
        $roles = parent::getRoles();
        if ($this->isAdmin) {
            $roles[] = 'ROLE_ADMIN';
        }
        return array_unique($roles);
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): static
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): static
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): static
    {
        $this->phone = $phone;
        return $this;
    }

    public function isAdmin(): ?bool
    {
        return $this->isAdmin;
    }

    public function setIsAdmin(bool $isAdmin): static
    {
        $this->isAdmin = $isAdmin;
        return $this;
    }

    /**
     * @return Collection<int, WorkTime>
     */
    public function getWorkTimes(): Collection
    {
        return $this->workTimes;
    }

    /**
     * @return Collection<int, Expense>
     */
    public function getExpenses(): Collection
    {
        return $this->expenses;
    }

    /**
     * @return Collection<int, Leave>
     */
    public function getLeaves(): Collection
    {
        return $this->leaves;
    }

    /**
     * Get the full name of the consultant
     */
    public function getFullName(): string
    {
        return "{$this->firstName} {$this->lastName}";
    }
}