<?php

namespace App\Service;

use App\Entity\Company;
use App\Repository\CompanyRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for managing company information
 */
class CompanyService
{
    /**
     * Default company settings
     */
    private const DEFAULT_COMPANY_NAME = 'Default Company';
    private const DEFAULT_ANNUAL_LEAVE_DAYS = 25;
    private const DEFAULT_WORK_HOURS_PER_DAY = 7.5;
    private const DEFAULT_MAX_REMOTE_WORK_PERCENTAGE = null; // null = unlimited remote work
    private const DEFAULT_REMOTE_WORK_PERCENTAGE = 20.0;
    private const DEFAULT_WORK_TIME_VALIDATION_REQUIRED = false;
    private const DEFAULT_HOLIDAY_PAYMENT_ENABLED = true;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly CompanyRepository $companyRepository,
        private readonly Security $security
    ) {}

    /**
     * Get company information, creating a default company if none exists
     */
    public function getCompanyInfo(): Company
    {
        $company = $this->companyRepository->findDefault();

        if (!$company) {
            $company = $this->createDefaultCompany();
        }

        return $company;
    }

    /**
     * Update company information (admin only)
     *
     * @param array<string, mixed> $data The company data to update
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    public function updateCompanyInfo(array $data): Company
    {
        $this->denyAccessUnlessAdmin();

        $company = $this->getCompanyInfo();
        $this->updateCompanyFields($company, $data);

        $this->entityManager->flush();

        return $company;
    }

    /**
     * Create a default company with standard settings
     */
    private function createDefaultCompany(): Company
    {
        $company = new Company();
        $company->setName(self::DEFAULT_COMPANY_NAME);
        $company->setAnnualLeaveDays(self::DEFAULT_ANNUAL_LEAVE_DAYS);
        $company->setWorkHoursPerDay(self::DEFAULT_WORK_HOURS_PER_DAY);
        $company->setMaxRemoteWorkPercentage(self::DEFAULT_MAX_REMOTE_WORK_PERCENTAGE);
        $company->setDefaultRemoteWorkPercentage(self::DEFAULT_REMOTE_WORK_PERCENTAGE);
        $company->setWorkTimeValidationRequired(self::DEFAULT_WORK_TIME_VALIDATION_REQUIRED);
        $company->setHolidayPaymentEnabled(self::DEFAULT_HOLIDAY_PAYMENT_ENABLED);

        $this->entityManager->persist($company);
        $this->entityManager->flush();

        return $company;
    }

    /**
     * Update company fields from an array of data
     *
     * @param Company $company The company entity to update
     * @param array<string, mixed> $data The data to apply
     */
    private function updateCompanyFields(Company $company, array $data): void
    {
        if (isset($data['name'])) {
            $company->setName($data['name']);
        }

        if (isset($data['address'])) {
            $company->setAddress($data['address']);
        }

        if (isset($data['phone'])) {
            $company->setPhone($data['phone']);
        }

        if (isset($data['email'])) {
            $company->setEmail($data['email']);
        }

        if (isset($data['website'])) {
            $company->setWebsite($data['website']);
        }

        if (isset($data['siret'])) {
            $company->setSiret($data['siret']);
        }

        if (isset($data['annualLeaveDays'])) {
            $company->setAnnualLeaveDays((int) $data['annualLeaveDays']);
        }

        if (isset($data['workHoursPerDay'])) {
            $company->setWorkHoursPerDay((float) $data['workHoursPerDay']);
        }

        if (isset($data['maxRemoteWorkPercentage'])) {
            $maxRemoteWork = $data['maxRemoteWorkPercentage'];
            // Allow null for unlimited remote work
            if ($maxRemoteWork === null) {
                $company->setMaxRemoteWorkPercentage(null);
            } else {
                $this->validateRemoteWorkPercentage((float) $maxRemoteWork);
                $company->setMaxRemoteWorkPercentage((float) $maxRemoteWork);
            }
        }

        if (isset($data['defaultRemoteWorkPercentage'])) {
            $company->setDefaultRemoteWorkPercentage((float) $data['defaultRemoteWorkPercentage']);
        }

        if (isset($data['workTimeValidationRequired'])) {
            $company->setWorkTimeValidationRequired((bool) $data['workTimeValidationRequired']);
        }

        if (isset($data['holidayPaymentEnabled'])) {
            $company->setHolidayPaymentEnabled((bool) $data['holidayPaymentEnabled']);
        }
    }

    /**
     * Validate remote work percentage value
     */
    private function validateRemoteWorkPercentage(float $percentage): void
    {
        if ($percentage < 0 || $percentage > 100) {
            throw new \InvalidArgumentException(
                'Remote work percentage must be between 0 and 100, or null for unlimited'
            );
        }
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Only administrators can update company information');
        }
    }
}
