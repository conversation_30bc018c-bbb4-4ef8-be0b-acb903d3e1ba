<?php

namespace App\Service;

use App\Entity\Consultant;
use App\Entity\Expense;
use App\Entity\Client;
use App\Entity\Trip;
use App\Entity\ExpenseCategory;
use App\Repository\ExpenseRepository;
use App\Repository\ConsultantRepository;
use App\Repository\ClientRepository;
use App\Repository\TripRepository;
use App\Repository\ExpenseCategoryRepository;
use DateTimeInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for managing expenses
 */
class ExpenseService
{
    /**
     * Default currency for the application
     */
    private const DEFAULT_CURRENCY = 'EUR';

    /**
     * Default exchange rate for the default currency
     */
    private const DEFAULT_EXCHANGE_RATE = '1.0000';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ExpenseRepository $expenseRepository,
        private readonly ConsultantRepository $consultantRepository,
        private readonly ClientRepository $clientRepository,
        private readonly TripRepository $tripRepository,
        private readonly ExpenseCategoryRepository $expenseCategoryRepository,
        private readonly CurrencyConverterService $currencyConverter,
        private readonly Security $security
    ) {}

    /**
     * Get all expenses (admin only)
     *
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     * @return array<Expense>
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    public function getAllExpenses(
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $this->denyAccessUnlessAdmin();

        if ($startDate || $endDate) {
            return $this->expenseRepository->findByDateRange($startDate, $endDate);
        }

        return $this->expenseRepository->findAll();
    }

    /**
     * Get all non-validated expenses (admin only)
     *
     * @return array<Expense>
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    public function getNonValidatedExpenses(): array
    {
        $this->denyAccessUnlessAdmin();
        return $this->expenseRepository->findNonValidated();
    }

    /**
     * Get an expense by ID
     *
     * @throws NotFoundHttpException If the expense is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getExpenseById(int $id): Expense
    {
        $expense = $this->expenseRepository->find($id);
        if (!$expense) {
            throw new NotFoundHttpException('Expense not found');
        }

        $this->checkAccess($expense);

        return $expense;
    }

    /**
     * Get expenses for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return array<Expense>
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getExpensesByConsultant(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->expenseRepository->findByConsultant($consultant, $startDate, $endDate);
    }

    /**
     * Create a new expense
     *
     * @param array<string, mixed> $data The expense data
     *
     * @throws \InvalidArgumentException If required data is missing
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function createExpense(array $data): Expense
    {
        $this->validateRequiredExpenseData($data);

        $consultant = $this->getConsultantOrFail($data['consultantId']);
        $this->checkConsultantAccess($consultant);

        $expense = new Expense();
        $expense->setConsultant($consultant);

        // Set client if provided
        if (isset($data['clientId'])) {
            $client = $this->clientRepository->find($data['clientId']);
            if ($client) {
                $expense->setClient($client);
            }
        }

        // Set trip if provided
        if (isset($data['tripId'])) {
            $trip = $this->tripRepository->find($data['tripId']);
            if ($trip) {
                // Verify trip belongs to the consultant
                if ($trip->getConsultant()->getId() === $consultant->getId()) {
                    $expense->setTrip($trip);
                    // If trip has a client and no client was specified, use trip's client
                    if (!isset($data['clientId']) && $trip->getClient()) {
                        $expense->setClient($trip->getClient());
                    }
                }
            }
        }

        // Set category if provided
        if (isset($data['categoryId'])) {
            $category = $this->expenseCategoryRepository->find($data['categoryId']);
            if ($category && $category->isActive()) {
                $expense->setCategory($category);
            }
        }

        if (isset($data['date'])) {
            $expense->setDate(new \DateTime($data['date']));
        } else {
            $expense->setDate(new \DateTime());
        }

        $expense->setDescription($data['description']);
        $expense->setAmount((string) $data['amount']);
        $expense->setCurrency($data['currency']);

        $this->convertAndSetEuroAmount($expense);

        if (isset($data['receipt'])) {
            $expense->setReceipt($data['receipt']);
        }

        $expense->setValidated(false);

        $this->entityManager->persist($expense);
        $this->entityManager->flush();

        return $expense;
    }

    /**
     * Update an existing expense
     *
     * @param int $id The expense ID
     * @param array<string, mixed> $data The updated data
     *
     * @throws NotFoundHttpException If the expense is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function updateExpense(int $id, array $data): Expense
    {
        $expense = $this->getExpenseById($id);
        $this->checkAccess($expense);

        if ($expense->isValidated() && !$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('You cannot modify a validated expense');
        }

        $recalculateAmount = false;

        if (isset($data['date'])) {
            $expense->setDate(new \DateTime($data['date']));
        }

        if (isset($data['description'])) {
            $expense->setDescription($data['description']);
        }

        if (isset($data['amount'])) {
            $expense->setAmount((string) $data['amount']);
            $recalculateAmount = true;
        }

        if (isset($data['currency'])) {
            $expense->setCurrency($data['currency']);
            $recalculateAmount = true;
        }

        if ($recalculateAmount) {
            $this->convertAndSetEuroAmount($expense);
        }

        if (isset($data['receipt'])) {
            $expense->setReceipt($data['receipt']);
        }

        $this->entityManager->flush();

        return $expense;
    }

    /**
     * Delete an expense
     *
     * @throws NotFoundHttpException If the expense is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function deleteExpense(int $id): void
    {
        $expense = $this->getExpenseById($id);
        $this->checkAccess($expense);

        if ($expense->isValidated() && !$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('You cannot delete a validated expense');
        }

        $this->entityManager->remove($expense);
        $this->entityManager->flush();
    }

    /**
     * Validate an expense (admin only)
     *
     * @throws NotFoundHttpException If the expense is not found
     * @throws AccessDeniedException If the user is not an admin
     */
    public function validateExpense(int $id): Expense
    {
        $expense = $this->getExpenseById($id);
        $this->denyAccessUnlessAdmin();

        $expense->setValidated(true);
        $this->entityManager->flush();

        return $expense;
    }

    /**
     * Calculate the total expenses for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return float The total amount in EUR
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function calculateTotal(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): float {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->expenseRepository->calculateTotal($consultant, $startDate, $endDate);
    }

    /**
     * Convert expense amount to EUR and set the related fields
     */
    private function convertAndSetEuroAmount(Expense $expense): void
    {
        if ($expense->getCurrency() === self::DEFAULT_CURRENCY) {
            $expense->setAmountEur($expense->getAmount());
            $expense->setExchangeRate(self::DEFAULT_EXCHANGE_RATE);
        } else {
            $conversionResult = $this->currencyConverter->convert(
                $expense->getCurrency(),
                self::DEFAULT_CURRENCY,
                (float) $expense->getAmount()
            );
            $expense->setAmountEur((string) $conversionResult['convertedAmount']);
            $expense->setExchangeRate((string) $conversionResult['exchangeRate']);
        }
    }

    /**
     * Validate required expense data
     *
     * @param array<string, mixed> $data The expense data to validate
     *
     * @throws \InvalidArgumentException If required data is missing
     */
    private function validateRequiredExpenseData(array $data): void
    {
        if (!isset($data['consultantId'])) {
            throw new \InvalidArgumentException('Consultant ID is required');
        }

        if (!isset($data['description'])) {
            throw new \InvalidArgumentException('Description is required');
        }

        if (!isset($data['amount'])) {
            throw new \InvalidArgumentException('Amount is required');
        }

        if (!isset($data['currency'])) {
            throw new \InvalidArgumentException('Currency is required');
        }
    }

    /**
     * Get a consultant by ID or throw an exception
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    private function getConsultantOrFail(int $id): Consultant
    {
        $consultant = $this->consultantRepository->find($id);
        if (!$consultant) {
            throw new NotFoundHttpException('Consultant not found');
        }
        return $consultant;
    }

    /**
     * Check if the current user has access to an expense
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkAccess(Expense $expense): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') && $currentUser->getId() !== $expense->getConsultant()->getId()) {
            throw new AccessDeniedException('You can only access your own expenses');
        }
    }

    /**
     * Check if the current user has access to a consultant's data
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkConsultantAccess(Consultant $consultant): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') && $currentUser->getId() !== $consultant->getId()) {
            throw new AccessDeniedException('You can only access your own data');
        }
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Admin access required');
        }
    }
}
