<?php

namespace App\Service;

use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;

/**
 * Service for converting amounts between currencies
 */
class CurrencyConverterService
{
    /**
     * API URL for currency conversion
     */
    private const API_URL = 'https://v6.exchangerate-api.com/v6/{api_key}/pair/{from}/{to}';

    /**
     * Cache lifetime for exchange rates in seconds (1 day)
     */
    private const CACHE_TTL = 86400;

    /**
     * Default currency for the application
     */
    private const DEFAULT_CURRENCY = 'EUR';

    /**
     * Fallback exchange rates to EUR (updated as of May 2023)
     * In a production environment, you would want to update these regularly
     */
    private const FALLBACK_RATES = [
        'USD' => 0.92,  // 1 USD = 0.92 EUR
        'GBP' => 1.17,  // 1 GBP = 1.17 EUR
        'JPY' => 0.0061, // 1 JPY = 0.0061 EUR
        'CHF' => 1.03,  // 1 CHF = 1.03 EUR
        'CAD' => 0.68,  // 1 CAD = 0.68 EUR
        'AUD' => 0.61,  // 1 AUD = 0.61 EUR
        'CNY' => 0.13,  // 1 CNY = 0.13 EUR
        'INR' => 0.011, // 1 INR = 0.011 EUR
        'EUR' => 1.0,   // 1 EUR = 1.0 EUR
    ];

    /**
     * @var HttpClientInterface The HTTP client for API requests
     */
    private HttpClientInterface $httpClient;

    /**
     * @var string|null The API key for the currency conversion service
     */
    private ?string $apiKey;

    public function __construct(
        private readonly ParameterBagInterface $params,
        private readonly ?LoggerInterface $logger = null,
        private readonly ?CacheInterface $cache = null,
        ?HttpClientInterface $httpClient = null
    ) {
        $this->httpClient = $httpClient ?? HttpClient::create();
        $this->apiKey = $params->has('app.currency_api_key') ? $params->get('app.currency_api_key') : null;
    }

    /**
     * Convert an amount from one currency to another
     *
     * @param float $amount The amount to convert
     * @param string $fromCurrency The source currency code (e.g., 'USD')
     * @param string $toCurrency The target currency code (e.g., 'EUR')
     *
     * @return array{convertedAmount: float, exchangeRate: float} The converted amount and exchange rate
     */
    public function convert(float $amount, string $fromCurrency, string $toCurrency = self::DEFAULT_CURRENCY): array
    {
        // If currencies are the same, no conversion needed
        if ($fromCurrency === $toCurrency) {
            return [
                'convertedAmount' => $amount,
                'exchangeRate' => 1.0,
            ];
        }

        $exchangeRate = $this->getExchangeRate($fromCurrency, $toCurrency);
        $convertedAmount = $amount * $exchangeRate;

        return [
            'convertedAmount' => $convertedAmount,
            'exchangeRate' => $exchangeRate,
        ];
    }

    /**
     * Get the exchange rate between two currencies
     *
     * @param string $fromCurrency The source currency code
     * @param string $toCurrency The target currency code
     *
     * @return float The exchange rate
     */
    private function getExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        // Try to get from cache first if available
        if ($this->cache) {
            $cacheKey = "currency_rate_{$fromCurrency}_{$toCurrency}";
            return $this->cache->get($cacheKey, function (ItemInterface $item) use ($fromCurrency, $toCurrency) {
                $item->expiresAfter(self::CACHE_TTL);
                return $this->fetchExchangeRate($fromCurrency, $toCurrency);
            });
        }

        // No cache, fetch directly
        return $this->fetchExchangeRate($fromCurrency, $toCurrency);
    }

    /**
     * Fetch the exchange rate from API or fallback
     *
     * @param string $fromCurrency The source currency code
     * @param string $toCurrency The target currency code
     *
     * @return float The exchange rate
     */
    private function fetchExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        // If API key is not set, use fallback rates
        if (!$this->apiKey) {
            $this->logWarning('No API key set for currency conversion, using fallback rates');
            return $this->getFallbackExchangeRate($fromCurrency, $toCurrency);
        }

        try {
            $url = str_replace(
                ['{api_key}', '{from}', '{to}'],
                [$this->apiKey, $fromCurrency, $toCurrency],
                self::API_URL
            );

            $response = $this->httpClient->request('GET', $url);

            if ($response->getStatusCode() === 200) {
                $data = $response->toArray();
                if (isset($data['conversion_rate'])) {
                    return (float) $data['conversion_rate'];
                }
            }

            $this->logWarning('Currency API returned invalid response', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'status' => $response->getStatusCode(),
            ]);

            // Fallback to backup rates if API call fails
            return $this->getFallbackExchangeRate($fromCurrency, $toCurrency);
        } catch (ExceptionInterface $e) {
            $this->logError('Currency API request failed', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage(),
            ]);

            // Fallback to backup rates if API call fails
            return $this->getFallbackExchangeRate($fromCurrency, $toCurrency);
        }
    }

    /**
     * Get fallback exchange rate when API is unavailable
     *
     * @param string $fromCurrency The source currency code
     * @param string $toCurrency The target currency code
     *
     * @return float The exchange rate
     */
    private function getFallbackExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        // If we don't have a rate for the requested currency, return 1.0
        if (!isset(self::FALLBACK_RATES[$fromCurrency])) {
            $this->logWarning('No fallback rate available for currency', ['currency' => $fromCurrency]);
            return 1.0;
        }

        // If target currency is EUR, return the direct rate
        if ($toCurrency === self::DEFAULT_CURRENCY) {
            return self::FALLBACK_RATES[$fromCurrency];
        }

        // If source currency is EUR, return the inverse of the target rate
        if ($fromCurrency === self::DEFAULT_CURRENCY && isset(self::FALLBACK_RATES[$toCurrency])) {
            return 1.0 / self::FALLBACK_RATES[$toCurrency];
        }

        // For cross-currency conversion, convert through EUR
        if (isset(self::FALLBACK_RATES[$fromCurrency]) && isset(self::FALLBACK_RATES[$toCurrency])) {
            $fromToEur = self::FALLBACK_RATES[$fromCurrency];
            $eurToTarget = 1.0 / self::FALLBACK_RATES[$toCurrency];
            return $fromToEur * $eurToTarget;
        }

        // Default fallback
        return 1.0;
    }

    /**
     * Log a warning message if logger is available
     *
     * @param string $message The message to log
     * @param array<string, mixed> $context Additional context data
     */
    private function logWarning(string $message, array $context = []): void
    {
        if ($this->logger) {
            $this->logger->warning('[CurrencyConverter] ' . $message, $context);
        }
    }

    /**
     * Log an error message if logger is available
     *
     * @param string $message The message to log
     * @param array<string, mixed> $context Additional context data
     */
    private function logError(string $message, array $context = []): void
    {
        if ($this->logger) {
            $this->logger->error('[CurrencyConverter] ' . $message, $context);
        }
    }
}
