<?php

namespace App\Service;

use App\Entity\Project;
use App\Entity\Client;
use App\Repository\ProjectRepository;
use App\Repository\ClientRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for managing projects
 */
class ProjectService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ProjectRepository $projectRepository,
        private readonly ClientRepository $clientRepository,
        private readonly Security $security
    ) {}

    /**
     * Get all projects (admin only)
     *
     * @return array<Project>
     */
    public function getAllProjects(): array
    {
        $this->denyAccessUnlessAdmin();
        return $this->projectRepository->findAll();
    }

    /**
     * Get all active projects
     *
     * @return array<Project>
     */
    public function getActiveProjects(): array
    {
        return $this->projectRepository->findActive();
    }

    /**
     * Get projects by client
     *
     * @param int $clientId
     * @param bool|null $activeOnly
     * @return array<Project>
     */
    public function getProjectsByClient(int $clientId, ?bool $activeOnly = true): array
    {
        $client = $this->getClientOrFail($clientId);
        return $this->projectRepository->findByClient($client, $activeOnly);
    }

    /**
     * Get a project by ID
     *
     * @throws NotFoundHttpException If the project is not found
     */
    public function getProjectById(int $id): Project
    {
        $project = $this->projectRepository->find($id);
        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }
        return $project;
    }

    /**
     * Create a new project (admin only)
     *
     * @param array<string, mixed> $data The project data
     *
     * @throws \InvalidArgumentException If required data is missing
     * @throws NotFoundHttpException If the client is not found
     */
    public function createProject(array $data): Project
    {
        $this->denyAccessUnlessAdmin();

        $this->validateProjectData($data, true);

        $client = $this->getClientOrFail($data['clientId']);

        // Check if code is unique
        if ($this->projectRepository->findByCode($data['code'])) {
            throw new \InvalidArgumentException('Project code already exists');
        }

        $project = new Project();
        $project->setName($data['name']);
        $project->setCode($data['code']);
        $project->setClient($client);

        if (isset($data['description'])) {
            $project->setDescription($data['description']);
        }

        if (isset($data['startDate'])) {
            $project->setStartDate(new \DateTime($data['startDate']));
        }

        if (isset($data['endDate'])) {
            $project->setEndDate(new \DateTime($data['endDate']));
        }

        if (isset($data['isActive'])) {
            $project->setIsActive((bool) $data['isActive']);
        }

        $this->entityManager->persist($project);
        $this->entityManager->flush();

        return $project;
    }

    /**
     * Update an existing project (admin only)
     *
     * @param int $id The project ID
     * @param array<string, mixed> $data The updated data
     *
     * @throws NotFoundHttpException If the project is not found
     */
    public function updateProject(int $id, array $data): Project
    {
        $this->denyAccessUnlessAdmin();

        $project = $this->getProjectById($id);
        $this->validateProjectData($data, false);

        if (isset($data['name'])) {
            $project->setName($data['name']);
        }

        if (isset($data['code'])) {
            // Check if code is unique (excluding current project)
            $existingProject = $this->projectRepository->findByCode($data['code']);
            if ($existingProject && $existingProject->getId() !== $project->getId()) {
                throw new \InvalidArgumentException('Project code already exists');
            }
            $project->setCode($data['code']);
        }

        if (isset($data['description'])) {
            $project->setDescription($data['description']);
        }

        if (isset($data['clientId'])) {
            $client = $this->getClientOrFail($data['clientId']);
            $project->setClient($client);
        }

        if (isset($data['startDate'])) {
            $project->setStartDate($data['startDate'] ? new \DateTime($data['startDate']) : null);
        }

        if (isset($data['endDate'])) {
            $project->setEndDate($data['endDate'] ? new \DateTime($data['endDate']) : null);
        }

        if (isset($data['isActive'])) {
            $project->setIsActive((bool) $data['isActive']);
        }

        $project->updateTimestamp();
        $this->entityManager->flush();

        return $project;
    }

    /**
     * Delete a project (admin only)
     *
     * @throws NotFoundHttpException If the project is not found
     */
    public function deleteProject(int $id): void
    {
        $this->denyAccessUnlessAdmin();

        $project = $this->getProjectById($id);

        // Check if project has associated work time activities
        if (!$project->getWorkTimeActivities()->isEmpty()) {
            throw new \InvalidArgumentException('Cannot delete project with associated work time activities');
        }

        $this->entityManager->remove($project);
        $this->entityManager->flush();
    }

    /**
     * Search projects
     *
     * @param string $search
     * @return array<Project>
     */
    public function searchProjects(string $search): array
    {
        return $this->projectRepository->search($search);
    }

    /**
     * Get a client by ID or throw an exception
     *
     * @throws NotFoundHttpException If the client is not found
     */
    private function getClientOrFail(int $id): Client
    {
        $client = $this->clientRepository->find($id);
        if (!$client) {
            throw new NotFoundHttpException('Client not found');
        }
        return $client;
    }

    /**
     * Validate project data
     *
     * @param array<string, mixed> $data
     * @param bool $isCreation
     */
    private function validateProjectData(array $data, bool $isCreation): void
    {
        if ($isCreation) {
            if (empty($data['name'])) {
                throw new \InvalidArgumentException('Project name is required');
            }

            if (empty($data['code'])) {
                throw new \InvalidArgumentException('Project code is required');
            }

            if (empty($data['clientId'])) {
                throw new \InvalidArgumentException('Client ID is required');
            }
        }

        // Validate dates if provided
        if (isset($data['startDate']) && isset($data['endDate'])) {
            $startDate = new \DateTime($data['startDate']);
            $endDate = new \DateTime($data['endDate']);
            
            if ($startDate > $endDate) {
                throw new \InvalidArgumentException('Start date cannot be after end date');
            }
        }
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Admin access required');
        }
    }
}
