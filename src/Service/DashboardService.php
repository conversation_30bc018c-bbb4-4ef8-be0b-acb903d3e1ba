<?php

namespace App\Service;

use App\Entity\Consultant;
use App\Repository\ConsultantRepository;
use DateTimeInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for generating dashboard data
 */
class DashboardService
{
    public function __construct(
        private readonly ConsultantRepository $consultantRepository,
        private readonly WorkTimeService $workTimeService,
        private readonly ExpenseService $expenseService,
        private readonly LeaveService $leaveService,
        private readonly Security $security
    ) {}

    /**
     * Get dashboard data for a specific consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface $startDate The start date for statistics
     * @param DateTimeInterface $endDate The end date for statistics
     *
     * @return array<string, mixed> The dashboard data
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getConsultantDashboard(int $consultantId, DateTimeInterface $startDate, DateTimeInterface $endDate): array
    {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        // Calculate work time statistics
        $workTimeStats = $this->workTimeService->calculateStatistics($consultantId, $startDate, $endDate);

        // Calculate expenses
        $totalExpenses = $this->expenseService->calculateTotal($consultantId, $startDate, $endDate);

        // Get remaining leave days
        $remainingLeaveDays = $this->leaveService->calculateLeaveBalance($consultantId);

        return [
            'consultant' => [
                'id' => $consultant->getId(),
                'lastName' => $consultant->getLastName(),
                'firstName' => $consultant->getFirstName(),
                'email' => $consultant->getEmail(),
            ],
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
            ],
            'workTime' => [
                'totalHours' => $workTimeStats['totalHours'] ?? 0,
                'remoteHours' => $workTimeStats['remoteHours'] ?? 0,
                'remoteWorkPercentage' => $workTimeStats['remoteWorkPercentage'] ?? 0,
            ],
            'expenses' => [
                'totalEur' => $totalExpenses,
            ],
            'leaves' => [
                'remainingDays' => $remainingLeaveDays,
                'year' => (int) date('Y'),
            ],
        ];
    }

    /**
     * Get dashboard data for administrators
     *
     * @param DateTimeInterface $startDate The start date for statistics
     * @param DateTimeInterface $endDate The end date for statistics
     *
     * @return array<string, mixed> The dashboard data
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    public function getAdminDashboard(DateTimeInterface $startDate, DateTimeInterface $endDate): array
    {
        $this->denyAccessUnlessAdmin();

        $consultants = $this->consultantRepository->findAll();

        $consultantStats = [];
        $totalHours = 0;
        $totalRemoteHours = 0;
        $totalExpenses = 0;

        foreach ($consultants as $consultant) {
            $consultantId = $consultant->getId();

            // Calculate work time statistics
            $workTimeStats = $this->workTimeService->calculateStatistics($consultantId, $startDate, $endDate);
            $totalWorkHours = $workTimeStats['totalHours'] ?? 0;
            $remoteWorkHours = $workTimeStats['remoteHours'] ?? 0;
            $remoteWorkPercentage = $workTimeStats['remoteWorkPercentage'] ?? 0;

            // Calculate expenses
            $expenses = $this->expenseService->calculateTotal($consultantId, $startDate, $endDate);

            $consultantStats[] = [
                'consultant' => [
                    'id' => $consultant->getId(),
                    'lastName' => $consultant->getLastName(),
                    'firstName' => $consultant->getFirstName(),
                ],
                'totalHours' => $totalWorkHours,
                'remoteHours' => $remoteWorkHours,
                'remoteWorkPercentage' => $remoteWorkPercentage,
                'totalExpenses' => $expenses,
            ];

            $totalHours += $totalWorkHours;
            $totalRemoteHours += $remoteWorkHours;
            $totalExpenses += $expenses;
        }

        $globalRemoteWorkPercentage = $totalHours > 0 ? ($totalRemoteHours / $totalHours) * 100 : 0;

        return [
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
            ],
            'global' => [
                'consultantCount' => count($consultants),
                'totalHours' => $totalHours,
                'remoteHours' => $totalRemoteHours,
                'remoteWorkPercentage' => $globalRemoteWorkPercentage,
                'totalExpenses' => $totalExpenses,
            ],
            'consultants' => $consultantStats,
        ];
    }

    /**
     * Get a consultant by ID or throw an exception
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    private function getConsultantOrFail(int $id): Consultant
    {
        $consultant = $this->consultantRepository->find($id);
        if (!$consultant) {
            throw new NotFoundHttpException('Consultant not found');
        }
        return $consultant;
    }

    /**
     * Check if the current user has access to a consultant's data
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkConsultantAccess(Consultant $consultant): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') &&
            ($currentUser instanceof Consultant) &&
            $currentUser->getId() !== $consultant->getId()) {
            throw new AccessDeniedException('You can only access your own dashboard');
        }
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Admin access required');
        }
    }
}
