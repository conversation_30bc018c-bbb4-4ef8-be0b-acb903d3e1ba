<?php

namespace App\Service;

use App\Entity\Consultant;
use App\Repository\ConsultantRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Service for managing consultants
 */
class ConsultantService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ConsultantRepository $consultantRepository,
        private readonly UserPasswordHasherInterface $passwordHasher,
        private readonly ValidatorInterface $validator
    ) {}

    /**
     * Get all consultants
     *
     * @return array<Consultant>
     */
    public function getAllConsultants(): array
    {
        return $this->consultantRepository->findAll();
    }

    /**
     * Get a consultant by ID
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    public function getConsultantById(int $id): Consultant
    {
        $consultant = $this->consultantRepository->find($id);
        if (!$consultant) {
            throw new NotFoundHttpException('Consultant not found');
        }
        return $consultant;
    }

    /**
     * Create a new consultant
     *
     * @param array<string, mixed> $data The consultant data
     *
     * @throws \InvalidArgumentException If validation fails
     */
    public function createConsultant(array $data): Consultant
    {
        $consultant = new Consultant();
        $this->updateConsultantData($consultant, $data);

        // Set password
        if (isset($data['password'])) {
            $consultant->setPassword(
                $this->passwordHasher->hashPassword($consultant, $data['password'])
            );
        }

        $this->validateConsultant($consultant);

        $this->entityManager->persist($consultant);
        $this->entityManager->flush();

        return $consultant;
    }

    /**
     * Update an existing consultant
     *
     * @param int $id The consultant ID
     * @param array<string, mixed> $data The updated data
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws \InvalidArgumentException If validation fails
     */
    public function updateConsultant(int $id, array $data): Consultant
    {
        $consultant = $this->getConsultantById($id);
        $this->updateConsultantData($consultant, $data);

        // Update password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $consultant->setPassword(
                $this->passwordHasher->hashPassword($consultant, $data['password'])
            );
        }

        $this->validateConsultant($consultant);

        $this->entityManager->flush();

        return $consultant;
    }

    /**
     * Delete a consultant
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    public function deleteConsultant(int $id): void
    {
        $consultant = $this->getConsultantById($id);
        $this->entityManager->remove($consultant);
        $this->entityManager->flush();
    }

    /**
     * Update consultant data from an array
     *
     * @param Consultant $consultant The consultant entity to update
     * @param array<string, mixed> $data The data to apply
     */
    private function updateConsultantData(Consultant $consultant, array $data): void
    {
        if (isset($data['email'])) {
            $consultant->setEmail($data['email']);
        }

        if (isset($data['lastName'])) {
            $consultant->setLastName($data['lastName']);
        }

        if (isset($data['firstName'])) {
            $consultant->setFirstName($data['firstName']);
        }

        if (array_key_exists('phone', $data)) {
            $consultant->setPhone($data['phone']);
        }

        if (isset($data['isAdmin'])) {
            $consultant->setIsAdmin((bool) $data['isAdmin']);
        }

        // Set default roles if not an admin
        $roles = ['ROLE_USER'];
        if ($consultant->isAdmin()) {
            $roles[] = 'ROLE_ADMIN';
        }
        $consultant->setRoles($roles);
    }

    /**
     * Validate a consultant entity
     *
     * @throws \InvalidArgumentException If validation fails
     */
    private function validateConsultant(Consultant $consultant): void
    {
        $errors = $this->validator->validate($consultant);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = $error->getMessage();
            }
            throw new \InvalidArgumentException(implode(', ', $errorMessages));
        }
    }
}
