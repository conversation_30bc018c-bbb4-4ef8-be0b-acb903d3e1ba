<?php

namespace App\Service;

use App\Entity\User;
use App\Validator\AuthValidator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * Service for user authentication and registration
 */
class AuthService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly UserPasswordHasherInterface $passwordHasher,
        private readonly AuthValidator $validator
    ) {}

    /**
     * Register a new user
     *
     * @param string $email The user's email
     * @param string $password The user's password
     *
     * @return User The created user
     *
     * @throws BadRequestHttpException If validation fails
     */
    public function register(string $email, string $password): User
    {
        $errors = $this->validator->validateRegistrationData(['email' => $email, 'password' => $password]);
        if (!empty($errors)) {
            throw new BadRequestHttpException(json_encode($errors));
        }

        $user = new User();
        $user->setEmail($email);
        $user->setPassword($this->passwordHasher->hashPassword($user, $password));
        $user->setRoles(['ROLE_USER']);

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }

    /**
     * Validate user login credentials
     *
     * @param User $user The user to validate
     *
     * @return bool Whether the credentials are valid
     */
    public function validateLoginCredentials(User $user): bool
    {
        return $this->validator->validateLoginCredentials($user);
    }
}