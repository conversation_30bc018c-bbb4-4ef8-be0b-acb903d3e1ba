<?php

namespace App\Service;

use App\Entity\Consultant;
use App\Entity\Leave;
use App\Repository\LeaveRepository;
use App\Repository\ConsultantRepository;
use App\Repository\CompanyRepository;
use DateTimeInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for managing leave requests
 */
class LeaveService
{
    /**
     * Leave status constants
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly LeaveRepository $leaveRepository,
        private readonly ConsultantRepository $consultantRepository,
        private readonly CompanyRepository $companyRepository,
        private readonly Security $security
    ) {}

    /**
     * Get all leave requests (admin only)
     *
     * @return array<Leave>
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    public function getAllLeaves(): array
    {
        $this->denyAccessUnlessAdmin();
        return $this->leaveRepository->findAll();
    }

    /**
     * Get all pending leave requests (admin only)
     *
     * @return array<Leave>
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    public function getPendingLeaves(): array
    {
        $this->denyAccessUnlessAdmin();
        return $this->leaveRepository->findPending();
    }

    /**
     * Get a leave request by ID
     *
     * @throws NotFoundHttpException If the leave is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getLeaveById(int $id): Leave
    {
        $leave = $this->leaveRepository->find($id);
        if (!$leave) {
            throw new NotFoundHttpException('Leave not found');
        }

        $this->checkAccess($leave);

        return $leave;
    }

    /**
     * Get leave requests for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return array<Leave>
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getLeavesByConsultant(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->leaveRepository->findByConsultant($consultant, $startDate, $endDate);
    }

    /**
     * Create a new leave request
     *
     * @param array<string, mixed> $data The leave data
     *
     * @throws \InvalidArgumentException If required data is missing or invalid
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     * @throws \RuntimeException If company configuration is not found
     */
    public function createLeave(array $data): Leave
    {
        $this->validateRequiredLeaveData($data);

        $consultant = $this->getConsultantOrFail($data['consultantId']);
        $this->checkConsultantAccess($consultant);

        $startDate = new \DateTime($data['startDate']);
        $endDate = new \DateTime($data['endDate']);

        if ($startDate > $endDate) {
            throw new \InvalidArgumentException('Start date must be before end date');
        }

        $numberOfDays = (float) $data['numberOfDays'];

        // Check if the consultant has enough leave days
        $balance = $this->calculateLeaveBalance($consultant->getId());
        if ($balance < $numberOfDays) {
            throw new \InvalidArgumentException('Not enough leave days available');
        }

        $leave = new Leave();
        $leave->setConsultant($consultant);
        $leave->setStartDate($startDate);
        $leave->setEndDate($endDate);
        $leave->setNumberOfDays($numberOfDays);
        $leave->setType($data['type']);

        if (isset($data['comment'])) {
            $leave->setComment($data['comment']);
        }

        $leave->setStatus(self::STATUS_PENDING);

        $this->entityManager->persist($leave);
        $this->entityManager->flush();

        return $leave;
    }

    /**
     * Update an existing leave request
     *
     * @param int $id The leave ID
     * @param array<string, mixed> $data The updated data
     *
     * @throws NotFoundHttpException If the leave is not found
     * @throws AccessDeniedException If the user doesn't have access
     * @throws \InvalidArgumentException If the leave balance would be negative
     */
    public function updateLeave(int $id, array $data): Leave
    {
        $leave = $this->getLeaveById($id);
        $this->checkAccess($leave);

        if ($leave->getStatus() !== self::STATUS_PENDING && !$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('You cannot modify a leave that is not pending');
        }

        $recalculateBalance = false;
        $oldNumberOfDays = $leave->getNumberOfDays();

        if (isset($data['startDate'])) {
            $leave->setStartDate(new \DateTime($data['startDate']));
            $recalculateBalance = true;
        }

        if (isset($data['endDate'])) {
            $leave->setEndDate(new \DateTime($data['endDate']));
            $recalculateBalance = true;
        }

        if (isset($data['numberOfDays'])) {
            $leave->setNumberOfDays((float) $data['numberOfDays']);
            $recalculateBalance = true;
        }

        if (isset($data['type'])) {
            $leave->setType($data['type']);
        }

        if (isset($data['comment'])) {
            $leave->setComment($data['comment']);
        }

        if ($recalculateBalance && !$this->security->isGranted('ROLE_ADMIN')) {
            // Check if the consultant has enough leave days
            $balance = $this->calculateLeaveBalance($leave->getConsultant()->getId());
            $newBalance = $balance + $oldNumberOfDays - $leave->getNumberOfDays();

            if ($newBalance < 0) {
                throw new \InvalidArgumentException('Not enough leave days available');
            }
        }

        $this->entityManager->flush();

        return $leave;
    }

    /**
     * Delete a leave request
     *
     * @throws NotFoundHttpException If the leave is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function deleteLeave(int $id): void
    {
        $leave = $this->getLeaveById($id);
        $this->checkAccess($leave);

        if ($leave->getStatus() !== self::STATUS_PENDING && !$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('You cannot delete a leave that is not pending');
        }

        $this->entityManager->remove($leave);
        $this->entityManager->flush();
    }

    /**
     * Approve a leave request (admin only)
     *
     * @throws NotFoundHttpException If the leave is not found
     * @throws AccessDeniedException If the user is not an admin
     * @throws \InvalidArgumentException If the leave is not pending
     */
    public function approveLeave(int $id): Leave
    {
        $leave = $this->getLeaveById($id);
        $this->denyAccessUnlessAdmin();

        if ($leave->getStatus() !== self::STATUS_PENDING) {
            throw new \InvalidArgumentException('Only pending leaves can be approved');
        }

        $leave->setStatus(self::STATUS_APPROVED);
        $this->entityManager->flush();

        return $leave;
    }

    /**
     * Reject a leave request (admin only)
     *
     * @param int $id The leave ID
     * @param string|null $reason Optional rejection reason
     *
     * @throws NotFoundHttpException If the leave is not found
     * @throws AccessDeniedException If the user is not an admin
     * @throws \InvalidArgumentException If the leave is not pending
     */
    public function rejectLeave(int $id, ?string $reason = null): Leave
    {
        $leave = $this->getLeaveById($id);
        $this->denyAccessUnlessAdmin();

        if ($leave->getStatus() !== self::STATUS_PENDING) {
            throw new \InvalidArgumentException('Only pending leaves can be rejected');
        }

        $leave->setStatus(self::STATUS_REJECTED);

        if ($reason) {
            $existingComment = $leave->getComment();
            $comment = $existingComment
                ? "$existingComment\n\nRejection reason: $reason"
                : "Rejection reason: $reason";
            $leave->setComment($comment);
        }

        $this->entityManager->flush();

        return $leave;
    }

    /**
     * Calculate the remaining leave balance for a consultant
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     * @throws \RuntimeException If company configuration is not found
     */
    public function calculateLeaveBalance(int $consultantId): float
    {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        $company = $this->companyRepository->findDefault();
        if (!$company) {
            throw new \RuntimeException('Company configuration not found');
        }

        $annualLeaveDays = $company->getAnnualLeaveDays();
        $currentYear = (int) date('Y');

        $usedDays = $this->leaveRepository->calculateUsedDays($consultant, $currentYear);

        return $annualLeaveDays - $usedDays;
    }

    /**
     * Validate required leave data
     *
     * @param array<string, mixed> $data The leave data to validate
     *
     * @throws \InvalidArgumentException If required data is missing
     */
    private function validateRequiredLeaveData(array $data): void
    {
        if (!isset($data['consultantId'])) {
            throw new \InvalidArgumentException('Consultant ID is required');
        }

        if (!isset($data['startDate']) || !isset($data['endDate'])) {
            throw new \InvalidArgumentException('Start date and end date are required');
        }

        if (!isset($data['numberOfDays'])) {
            throw new \InvalidArgumentException('Number of days is required');
        }

        if (!isset($data['type'])) {
            throw new \InvalidArgumentException('Leave type is required');
        }
    }

    /**
     * Get a consultant by ID or throw an exception
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    private function getConsultantOrFail(int $id): Consultant
    {
        $consultant = $this->consultantRepository->find($id);
        if (!$consultant) {
            throw new NotFoundHttpException('Consultant not found');
        }
        return $consultant;
    }

    /**
     * Check if the current user has access to a leave record
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkAccess(Leave $leave): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') && $currentUser->getId() !== $leave->getConsultant()->getId()) {
            throw new AccessDeniedException('You can only access your own leaves');
        }
    }

    /**
     * Check if the current user has access to a consultant's data
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkConsultantAccess(Consultant $consultant): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') && $currentUser->getId() !== $consultant->getId()) {
            throw new AccessDeniedException('You can only access your own data');
        }
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Admin access required');
        }
    }
}
