<?php

namespace App\Service;

use App\Entity\Consultant;
use App\Entity\WorkTime;
use App\Entity\WorkTimeActivity;
use App\Repository\WorkTimeRepository;
use App\Repository\WorkTimeActivityRepository;
use App\Repository\ConsultantRepository;
use App\Repository\ClientRepository;
use DateTimeInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for managing work time records
 */
class WorkTimeService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkTimeRepository $workTimeRepository,
        private readonly WorkTimeActivityRepository $workTimeActivityRepository,
        private readonly ConsultantRepository $consultantRepository,
        private readonly ClientRepository $clientRepository,
        private readonly CompanyService $companyService,
        private readonly Security $security
    ) {}

    /**
     * Get all work time records (admin only)
     *
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     * @return array<WorkTime>
     */
    public function getAllWorkTimes(
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $this->denyAccessUnlessAdmin();

        if ($startDate || $endDate) {
            return $this->workTimeRepository->findByDateRange($startDate, $endDate);
        }

        return $this->workTimeRepository->findAll();
    }

    /**
     * Get a work time record by ID
     *
     * @throws NotFoundHttpException If the work time is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getWorkTimeById(int $id): WorkTime
    {
        $workTime = $this->workTimeRepository->find($id);
        if (!$workTime) {
            throw new NotFoundHttpException('Work time not found');
        }

        $this->checkAccess($workTime);

        return $workTime;
    }

    /**
     * Get work time records for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return array<WorkTime>
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getWorkTimesByConsultant(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->workTimeRepository->findByConsultant($consultant, $startDate, $endDate);
    }

    /**
     * Create a new work time record
     *
     * @param array<string, mixed> $data The work time data
     *
     * @throws \InvalidArgumentException If required data is missing
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function createWorkTime(array $data): WorkTime
    {
        $currentUser = $this->security->getUser();

        if (!$currentUser instanceof Consultant) {
            throw new AccessDeniedException('You must be logged in as a consultant to create work time entries');
        }

        // If consultantId is provided and user is admin, use that consultant
        if (isset($data['consultantId']) && $this->security->isGranted('ROLE_ADMIN')) {
            $consultant = $this->getConsultantOrFail($data['consultantId']);
        } else {
            // Otherwise use the current user
            $consultant = $currentUser;
        }

        $workTime = new WorkTime();
        $workTime->setConsultant($consultant);

        // Set default remote work percentage from company settings
        $company = $this->companyService->getCompanyInfo();
        $workTime->setRemoteWorkPercentage($company->getDefaultRemoteWorkPercentage() ?? 20.0);

        $this->updateWorkTimeData($workTime, $data);

        // Handle activities if provided
        if (isset($data['activities']) && is_array($data['activities'])) {
            $this->updateWorkTimeActivities($workTime, $data['activities']);
        }

        $this->entityManager->persist($workTime);
        $this->entityManager->flush();

        return $workTime;
    }

    /**
     * Update an existing work time record
     *
     * @param int $id The work time ID
     * @param array<string, mixed> $data The updated data
     *
     * @throws NotFoundHttpException If the work time is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function updateWorkTime(int $id, array $data): WorkTime
    {
        $workTime = $this->getWorkTimeById($id);
        $this->checkAccess($workTime);

        $this->updateWorkTimeData($workTime, $data);
        $this->entityManager->flush();

        return $workTime;
    }

    /**
     * Delete a work time record
     *
     * @throws NotFoundHttpException If the work time is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function deleteWorkTime(int $id): void
    {
        $workTime = $this->getWorkTimeById($id);
        $this->checkAccess($workTime);

        $this->entityManager->remove($workTime);
        $this->entityManager->flush();
    }

    /**
     * Calculate work time statistics for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return array<string, mixed> The statistics
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function calculateStatistics(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->workTimeRepository->calculateStatistics($consultant, $startDate, $endDate);
    }

    /**
     * Update work time data from an array
     *
     * @param WorkTime $workTime The work time entity to update
     * @param array<string, mixed> $data The data to apply
     */
    private function updateWorkTimeData(WorkTime $workTime, array $data): void
    {
        // Handle period-based fields
        if (isset($data['startDate'])) {
            $workTime->setStartDate(new \DateTime($data['startDate']));
        }

        if (isset($data['endDate'])) {
            $workTime->setEndDate(new \DateTime($data['endDate']));
        }

        if (isset($data['totalHours'])) {
            $workTime->setTotalHours((float) $data['totalHours']);
        }

        if (isset($data['remoteWorkPercentage'])) {
            $this->validateRemoteWorkPercentage((float) $data['remoteWorkPercentage']);
            $workTime->setRemoteWorkPercentage((float) $data['remoteWorkPercentage']);
        }

        if (isset($data['notes'])) {
            $workTime->setNotes($data['notes']);
        }

        // Legacy fields for backward compatibility
        if (isset($data['date'])) {
            $workTime->setDate(new \DateTime($data['date']));
        } elseif (!$workTime->getId() && !isset($data['startDate'])) {
            // Only set default date for new entities if no period dates provided
            $workTime->setDate(new \DateTime());
        }

        if (isset($data['hours'])) {
            $workTime->setHours((float) $data['hours']);
            // Also set as totalHours if not explicitly provided
            if (!isset($data['totalHours'])) {
                $workTime->setTotalHours((float) $data['hours']);
            }
        }

        if (isset($data['remoteWork'])) {
            $workTime->setRemoteWork((bool) $data['remoteWork']);
            // Convert boolean to percentage if not explicitly provided
            if (!isset($data['remoteWorkPercentage'])) {
                $workTime->setRemoteWorkPercentage($data['remoteWork'] ? 100.0 : 0.0);
            }
        }

        if (isset($data['activity'])) {
            $workTime->setActivity($data['activity']);
        }

        if (isset($data['comment'])) {
            $workTime->setComment($data['comment']);
            // Also set as notes if not explicitly provided
            if (!isset($data['notes'])) {
                $workTime->setNotes($data['comment']);
            }
        }

        $workTime->updateTimestamp();
    }

    /**
     * Get a consultant by ID or throw an exception
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    private function getConsultantOrFail(int $id): Consultant
    {
        $consultant = $this->consultantRepository->find($id);
        if (!$consultant) {
            throw new NotFoundHttpException('Consultant not found');
        }
        return $consultant;
    }

    /**
     * Check if the current user has access to a work time record
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkAccess(WorkTime $workTime): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') &&
            ($currentUser instanceof Consultant) &&
            $currentUser->getId() !== $workTime->getConsultant()->getId()) {
            throw new AccessDeniedException('You can only access your own work times');
        }
    }

    /**
     * Check if the current user has access to a consultant's data
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkConsultantAccess(Consultant $consultant): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') &&
            ($currentUser instanceof Consultant) &&
            $currentUser->getId() !== $consultant->getId()) {
            throw new AccessDeniedException('You can only access your own data');
        }
    }

    /**
     * Update activities for a work time entry
     *
     * @param WorkTime $workTime The work time entity
     * @param array $activitiesData Array of activity data
     */
    private function updateWorkTimeActivities(WorkTime $workTime, array $activitiesData): void
    {
        // Remove existing activities
        foreach ($workTime->getActivities() as $activity) {
            $this->entityManager->remove($activity);
        }
        $workTime->getActivities()->clear();

        // Add new activities
        foreach ($activitiesData as $activityData) {
            $activity = new WorkTimeActivity();
            $activity->setWorkTime($workTime);
            $activity->setActivityName($activityData['activityName']);
            $activity->setHours((float) $activityData['hours']);
            $activity->setDescription($activityData['description'] ?? null);
            $activity->setIsBillable($activityData['isBillable'] ?? false);

            if (isset($activityData['clientId']) && $activityData['clientId']) {
                $client = $this->clientRepository->find($activityData['clientId']);
                if ($client) {
                    $activity->setClient($client);
                }
            }

            $workTime->addActivity($activity);
            $this->entityManager->persist($activity);
        }
    }

    /**
     * Validate a work time entry (admin only)
     */
    public function validateWorkTime(int $workTimeId): WorkTime
    {
        $this->denyAccessUnlessAdmin();

        $workTime = $this->getWorkTimeById($workTimeId);

        if ($workTime->isValidated()) {
            throw new \InvalidArgumentException('Work time entry is already validated');
        }

        $currentUser = $this->security->getUser();
        if (!$currentUser instanceof Consultant) {
            throw new AccessDeniedException('Invalid user type');
        }

        $workTime->setIsValidated(true);
        $workTime->setValidatedAt(new \DateTime());
        $workTime->setValidatedBy($currentUser);
        $workTime->updateTimestamp();

        $this->entityManager->flush();

        return $workTime;
    }

    /**
     * Validate remote work percentage against company limits
     */
    private function validateRemoteWorkPercentage(float $percentage): void
    {
        $company = $this->companyService->getCompanyInfo();
        $maxPercentage = $company->getMaxRemoteWorkPercentage() ?? 100.0;

        if ($percentage > $maxPercentage) {
            throw new \InvalidArgumentException(
                "Remote work percentage cannot exceed {$maxPercentage}%"
            );
        }

        if ($percentage < 0 || $percentage > 100) {
            throw new \InvalidArgumentException(
                'Remote work percentage must be between 0 and 100'
            );
        }
    }

    /**
     * Create a monthly work time template
     */
    public function createMonthlyTemplate(int $consultantId, int $year, int $month): WorkTime
    {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        // Calculate start and end dates for the month
        $startDate = new \DateTime("{$year}-{$month}-01");
        $endDate = clone $startDate;
        $endDate->modify('last day of this month');

        $company = $this->companyService->getCompanyInfo();
        $workingDaysInMonth = $this->calculateWorkingDays($startDate, $endDate);
        $totalHours = $workingDaysInMonth * ($company->getWorkHoursPerDay() ?? 7.5);

        $workTime = new WorkTime();
        $workTime->setConsultant($consultant);
        $workTime->setStartDate($startDate);
        $workTime->setEndDate($endDate);
        $workTime->setTotalHours($totalHours);
        $workTime->setRemoteWorkPercentage($company->getDefaultRemoteWorkPercentage() ?? 20.0);
        $workTime->setNotes("Monthly template for {$startDate->format('F Y')}");

        $this->entityManager->persist($workTime);
        $this->entityManager->flush();

        return $workTime;
    }

    /**
     * Calculate working days in a period (excluding weekends)
     */
    private function calculateWorkingDays(\DateTime $startDate, \DateTime $endDate): int
    {
        $workingDays = 0;
        $current = clone $startDate;

        while ($current <= $endDate) {
            $dayOfWeek = (int) $current->format('N'); // 1 = Monday, 7 = Sunday
            if ($dayOfWeek < 6) { // Monday to Friday
                $workingDays++;
            }
            $current->modify('+1 day');
        }

        return $workingDays;
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Admin access required');
        }
    }
}
