<?php

namespace App\Service;

use App\Entity\Consultant;
use App\Entity\WorkTime;
use App\Repository\WorkTimeRepository;
use App\Repository\ConsultantRepository;
use DateTimeInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\SecurityBundle\Security;

/**
 * Service for managing work time records
 */
class WorkTimeService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkTimeRepository $workTimeRepository,
        private readonly ConsultantRepository $consultantRepository,
        private readonly Security $security
    ) {}

    /**
     * Get all work time records (admin only)
     *
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     * @return array<WorkTime>
     */
    public function getAllWorkTimes(
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $this->denyAccessUnlessAdmin();

        if ($startDate || $endDate) {
            return $this->workTimeRepository->findByDateRange($startDate, $endDate);
        }

        return $this->workTimeRepository->findAll();
    }

    /**
     * Get a work time record by ID
     *
     * @throws NotFoundHttpException If the work time is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getWorkTimeById(int $id): WorkTime
    {
        $workTime = $this->workTimeRepository->find($id);
        if (!$workTime) {
            throw new NotFoundHttpException('Work time not found');
        }

        $this->checkAccess($workTime);

        return $workTime;
    }

    /**
     * Get work time records for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return array<WorkTime>
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function getWorkTimesByConsultant(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->workTimeRepository->findByConsultant($consultant, $startDate, $endDate);
    }

    /**
     * Create a new work time record
     *
     * @param array<string, mixed> $data The work time data
     *
     * @throws \InvalidArgumentException If required data is missing
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function createWorkTime(array $data): WorkTime
    {
        $currentUser = $this->security->getUser();

        if (!$currentUser instanceof Consultant) {
            throw new AccessDeniedException('You must be logged in as a consultant to create work time entries');
        }

        // If consultantId is provided and user is admin, use that consultant
        if (isset($data['consultantId']) && $this->security->isGranted('ROLE_ADMIN')) {
            $consultant = $this->getConsultantOrFail($data['consultantId']);
        } else {
            // Otherwise use the current user
            $consultant = $currentUser;
        }

        $workTime = new WorkTime();
        $workTime->setConsultant($consultant);

        $this->updateWorkTimeData($workTime, $data);

        $this->entityManager->persist($workTime);
        $this->entityManager->flush();

        return $workTime;
    }

    /**
     * Update an existing work time record
     *
     * @param int $id The work time ID
     * @param array<string, mixed> $data The updated data
     *
     * @throws NotFoundHttpException If the work time is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function updateWorkTime(int $id, array $data): WorkTime
    {
        $workTime = $this->getWorkTimeById($id);
        $this->checkAccess($workTime);

        $this->updateWorkTimeData($workTime, $data);
        $this->entityManager->flush();

        return $workTime;
    }

    /**
     * Delete a work time record
     *
     * @throws NotFoundHttpException If the work time is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function deleteWorkTime(int $id): void
    {
        $workTime = $this->getWorkTimeById($id);
        $this->checkAccess($workTime);

        $this->entityManager->remove($workTime);
        $this->entityManager->flush();
    }

    /**
     * Calculate work time statistics for a consultant
     *
     * @param int $consultantId The consultant ID
     * @param DateTimeInterface|null $startDate Optional start date filter
     * @param DateTimeInterface|null $endDate Optional end date filter
     *
     * @return array<string, mixed> The statistics
     *
     * @throws NotFoundHttpException If the consultant is not found
     * @throws AccessDeniedException If the user doesn't have access
     */
    public function calculateStatistics(
        int $consultantId,
        ?DateTimeInterface $startDate = null,
        ?DateTimeInterface $endDate = null
    ): array {
        $consultant = $this->getConsultantOrFail($consultantId);
        $this->checkConsultantAccess($consultant);

        return $this->workTimeRepository->calculateStatistics($consultant, $startDate, $endDate);
    }

    /**
     * Update work time data from an array
     *
     * @param WorkTime $workTime The work time entity to update
     * @param array<string, mixed> $data The data to apply
     */
    private function updateWorkTimeData(WorkTime $workTime, array $data): void
    {
        if (isset($data['date'])) {
            $workTime->setDate(new \DateTime($data['date']));
        } elseif (!$workTime->getId()) {
            // Only set default date for new entities
            $workTime->setDate(new \DateTime());
        }

        if (isset($data['hours'])) {
            $workTime->setHours((float) $data['hours']);
        }

        if (isset($data['remoteWork'])) {
            $workTime->setRemoteWork((bool) $data['remoteWork']);
        }

        if (isset($data['activity'])) {
            $workTime->setActivity($data['activity']);
        }

        if (isset($data['comment'])) {
            $workTime->setComment($data['comment']);
        }
    }

    /**
     * Get a consultant by ID or throw an exception
     *
     * @throws NotFoundHttpException If the consultant is not found
     */
    private function getConsultantOrFail(int $id): Consultant
    {
        $consultant = $this->consultantRepository->find($id);
        if (!$consultant) {
            throw new NotFoundHttpException('Consultant not found');
        }
        return $consultant;
    }

    /**
     * Check if the current user has access to a work time record
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkAccess(WorkTime $workTime): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') &&
            ($currentUser instanceof Consultant) &&
            $currentUser->getId() !== $workTime->getConsultant()->getId()) {
            throw new AccessDeniedException('You can only access your own work times');
        }
    }

    /**
     * Check if the current user has access to a consultant's data
     *
     * @throws AccessDeniedException If the user doesn't have access
     */
    private function checkConsultantAccess(Consultant $consultant): void
    {
        $currentUser = $this->security->getUser();
        if (!$this->security->isGranted('ROLE_ADMIN') &&
            ($currentUser instanceof Consultant) &&
            $currentUser->getId() !== $consultant->getId()) {
            throw new AccessDeniedException('You can only access your own data');
        }
    }

    /**
     * Deny access unless the user is an admin
     *
     * @throws AccessDeniedException If the user is not an admin
     */
    private function denyAccessUnlessAdmin(): void
    {
        if (!$this->security->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedException('Admin access required');
        }
    }
}
