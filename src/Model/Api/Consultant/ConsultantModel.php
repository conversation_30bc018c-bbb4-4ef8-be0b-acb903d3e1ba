<?php

namespace App\Model\Api\Consultant;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'Consultant',
    description: 'Consultant model'
)]
class ConsultantModel
{
    #[OA\Property(
        description: 'Consultant ID',
        example: 1
    )]
    public int $id;

    #[OA\Property(
        description: 'Consultant email',
        example: '<EMAIL>'
    )]
    public string $email;

    #[OA\Property(
        description: 'Consultant first name',
        example: 'John'
    )]
    public string $firstName;

    #[OA\Property(
        description: 'Consultant last name',
        example: 'Doe'
    )]
    public string $lastName;

    #[OA\Property(
        description: 'Consultant phone number',
        example: '+33123456789'
    )]
    public ?string $phone = null;

    #[OA\Property(
        description: 'Whether the consultant is an admin',
        example: false
    )]
    public bool $isAdmin;

    #[OA\Property(
        description: 'Consultant roles',
        type: 'array',
        items: new OA\Items(type: 'string'),
        example: ['ROLE_USER']
    )]
    public array $roles;

    #[OA\Property(
        description: 'Creation date',
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T12:00:00+00:00'
    )]
    public string $createdAt;

    #[OA\Property(
        description: 'Last update date',
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T12:00:00+00:00'
    )]
    public string $updatedAt;
}
