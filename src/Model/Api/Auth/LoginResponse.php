<?php

namespace App\Model\Api\Auth;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'LoginResponse',
    description: 'Login response model'
)]
class LoginResponse
{
    #[OA\Property(
        description: 'JWT token',
        example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
    )]
    public string $token;

    #[OA\Property(
        description: 'User information'
    )]
    public UserInfo $user;
}
