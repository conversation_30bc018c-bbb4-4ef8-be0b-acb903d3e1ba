<?php

namespace App\Model\Api\Auth;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'UserInfo',
    description: 'User information'
)]
class UserInfo
{
    #[OA\Property(
        description: 'User ID',
        example: 1
    )]
    public int $id;

    #[OA\Property(
        description: 'User email',
        example: '<EMAIL>'
    )]
    public string $email;

    #[OA\Property(
        description: 'User first name',
        example: 'John'
    )]
    public string $firstName;

    #[OA\Property(
        description: 'User last name',
        example: 'Doe'
    )]
    public string $lastName;

    #[OA\Property(
        description: 'User roles',
        type: 'array',
        items: new OA\Items(type: 'string'),
        example: ['ROLE_USER']
    )]
    public array $roles;

    #[OA\Property(
        description: 'Whether the user is an admin',
        example: false
    )]
    public bool $isAdmin;
}
