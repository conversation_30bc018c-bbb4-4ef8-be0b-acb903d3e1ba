<?php

namespace App\Model\Api\Auth;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'RegistrationUserInfo',
    description: 'User information after registration'
)]
class RegistrationUserInfo
{
    #[OA\Property(
        description: 'User ID',
        example: 1
    )]
    public int $id;

    #[OA\Property(
        description: 'User email',
        example: '<EMAIL>'
    )]
    public string $email;

    #[OA\Property(
        description: 'User first name',
        example: '<PERSON>'
    )]
    public string $firstName;

    #[OA\Property(
        description: 'User last name',
        example: '<PERSON><PERSON>'
    )]
    public string $lastName;
}
