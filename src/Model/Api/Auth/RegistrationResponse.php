<?php

namespace App\Model\Api\Auth;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'RegistrationResponse',
    description: 'Registration response model'
)]
class RegistrationResponse
{
    #[OA\Property(
        description: 'Success message',
        example: 'User registered successfully'
    )]
    public string $message;

    #[OA\Property(
        description: 'User information'
    )]
    public RegistrationUserInfo $user;
}
