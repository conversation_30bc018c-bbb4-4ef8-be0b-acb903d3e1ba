<?php

namespace App\Model\Api\Auth;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'LoginRequest',
    description: 'Login request model',
    required: ['email', 'password']
)]
class LoginRequest
{
    #[OA\Property(
        description: 'User email',
        example: '<EMAIL>'
    )]
    public string $email;

    #[OA\Property(
        description: 'User password',
        example: 'password123'
    )]
    public string $password;
}
