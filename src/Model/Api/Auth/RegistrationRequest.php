<?php

namespace App\Model\Api\Auth;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'RegistrationRequest',
    description: 'Registration request model',
    required: ['email', 'password', 'firstName', 'lastName']
)]
class RegistrationRequest
{
    #[OA\Property(
        description: 'User email',
        example: '<EMAIL>'
    )]
    public string $email;

    #[OA\Property(
        description: 'User password',
        example: 'password123'
    )]
    public string $password;

    #[OA\Property(
        description: 'User first name',
        example: '<PERSON>'
    )]
    public string $firstName;

    #[OA\Property(
        description: 'User last name',
        example: 'Doe'
    )]
    public string $lastName;

    #[OA\Property(
        description: 'User phone number',
        example: '+33123456789'
    )]
    public ?string $phone = null;
}
