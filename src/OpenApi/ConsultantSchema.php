<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

/**
 * OpenAPI schema for Consultant entity
 */
#[OA\Schema(
    schema: 'Consultant',
    description: 'Consultant model',
    title: 'Consultant'
)]
class ConsultantSchema
{
    #[OA\Property(
        property: 'id',
        description: 'Unique identifier',
        type: 'integer',
        example: 1
    )]
    private int $id;

    #[OA\Property(
        property: 'email',
        description: 'Email address',
        type: 'string',
        format: 'email',
        example: '<EMAIL>'
    )]
    private string $email;

    #[OA\Property(
        property: 'firstName',
        description: 'First name',
        type: 'string',
        example: 'John'
    )]
    private string $firstName;

    #[OA\Property(
        property: 'lastName',
        description: 'Last name',
        type: 'string',
        example: 'Doe'
    )]
    private string $lastName;

    #[OA\Property(
        property: 'phone',
        description: 'Phone number',
        type: 'string',
        nullable: true,
        example: '+33123456789'
    )]
    private ?string $phone;

    #[OA\Property(
        property: 'isAdmin',
        description: 'Whether the consultant has admin privileges',
        type: 'boolean',
        example: false
    )]
    private bool $isAdmin;

    #[OA\Property(
        property: 'roles',
        description: 'User roles',
        type: 'array',
        items: new OA\Items(type: 'string'),
        example: ['ROLE_USER']
    )]
    private array $roles;
}
