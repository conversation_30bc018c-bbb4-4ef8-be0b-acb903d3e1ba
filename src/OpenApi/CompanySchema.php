<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

/**
 * OpenAPI schema for Company entity
 */
#[OA\Schema(
    schema: 'Company',
    description: 'Company information model',
    title: 'Company'
)]
class CompanySchema
{
    #[OA\Property(
        property: 'id',
        description: 'Unique identifier',
        type: 'integer',
        example: 1
    )]
    private int $id;

    #[OA\Property(
        property: 'name',
        description: 'Company name',
        type: 'string',
        example: 'Acme Consulting'
    )]
    private string $name;

    #[OA\Property(
        property: 'address',
        description: 'Company address',
        type: 'string',
        nullable: true,
        example: '123 Main Street, 75001 Paris, France'
    )]
    private ?string $address;

    #[OA\Property(
        property: 'phone',
        description: 'Company phone number',
        type: 'string',
        nullable: true,
        example: '+33123456789'
    )]
    private ?string $phone;

    #[OA\Property(
        property: 'email',
        description: 'Company email address',
        type: 'string',
        nullable: true,
        format: 'email',
        example: '<EMAIL>'
    )]
    private ?string $email;

    #[OA\Property(
        property: 'website',
        description: 'Company website',
        type: 'string',
        nullable: true,
        example: 'https://www.acme-consulting.com'
    )]
    private ?string $website;

    #[OA\Property(
        property: 'siret',
        description: 'Company SIRET number',
        type: 'string',
        nullable: true,
        example: '12345678901234'
    )]
    private ?string $siret;

    #[OA\Property(
        property: 'annualLeaveDays',
        description: 'Default annual leave days for employees',
        type: 'integer',
        example: 25
    )]
    private int $annualLeaveDays;

    #[OA\Property(
        property: 'workHoursPerDay',
        description: 'Default work hours per day',
        type: 'number',
        format: 'float',
        example: 7.5
    )]
    private float $workHoursPerDay;

    #[OA\Property(
        property: 'maxRemoteWorkPercentage',
        description: 'Maximum allowed remote work percentage',
        type: 'number',
        format: 'float',
        example: 50.0
    )]
    private float $maxRemoteWorkPercentage;
}
