<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

/**
 * OpenAPI schema for Expense entity
 */
#[OA\Schema(
    schema: 'Expense',
    description: 'Expense record model',
    title: 'Expense'
)]
class ExpenseSchema
{
    #[OA\Property(
        property: 'id',
        description: 'Unique identifier',
        type: 'integer',
        example: 1
    )]
    private int $id;

    #[OA\Property(
        property: 'consultant',
        description: 'The consultant who submitted the expense',
        ref: '#/components/schemas/Consultant'
    )]
    private object $consultant;

    #[OA\Property(
        property: 'date',
        description: 'Date of the expense',
        type: 'string',
        format: 'date',
        example: '2023-06-15'
    )]
    private string $date;

    #[OA\Property(
        property: 'description',
        description: 'Description of the expense',
        type: 'string',
        example: 'Train ticket to client site'
    )]
    private string $description;

    #[OA\Property(
        property: 'amount',
        description: 'Amount of the expense in the original currency',
        type: 'string',
        example: '125.50'
    )]
    private string $amount;

    #[OA\Property(
        property: 'currency',
        description: 'Currency of the expense',
        type: 'string',
        example: 'EUR'
    )]
    private string $currency;

    #[OA\Property(
        property: 'amountEur',
        description: 'Amount of the expense converted to EUR',
        type: 'string',
        example: '125.50'
    )]
    private string $amountEur;

    #[OA\Property(
        property: 'exchangeRate',
        description: 'Exchange rate used for conversion to EUR',
        type: 'string',
        example: '1.0000'
    )]
    private string $exchangeRate;

    #[OA\Property(
        property: 'receipt',
        description: 'Receipt file path or identifier',
        type: 'string',
        nullable: true,
        example: 'receipts/2023/06/15/receipt-123.pdf'
    )]
    private ?string $receipt;

    #[OA\Property(
        property: 'validated',
        description: 'Whether the expense has been validated',
        type: 'boolean',
        example: false
    )]
    private bool $validated;
}
