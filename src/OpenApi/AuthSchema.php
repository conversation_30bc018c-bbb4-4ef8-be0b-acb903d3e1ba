<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

/**
 * OpenAPI schemas for authentication
 */
class AuthSchema
{
}

/**
 * Login request schema
 */
#[OA\Schema(
    schema: 'LoginRequest',
    description: 'Login request model',
    required: ['email', 'password'],
    title: 'LoginRequest'
)]
class LoginRequest
{
    #[OA\Property(
        property: 'email',
        description: 'User email',
        type: 'string',
        format: 'email',
        example: '<EMAIL>'
    )]
    private string $email;

    #[OA\Property(
        property: 'password',
        description: 'User password',
        type: 'string',
        format: 'password',
        example: 'password123'
    )]
    private string $password;
}

/**
 * Login response schema
 */
#[OA\Schema(
    schema: 'LoginResponse',
    description: 'Login response model',
    title: 'LoginResponse'
)]
class LoginResponse
{
    #[OA\Property(
        property: 'token',
        description: 'JWT token',
        type: 'string',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    )]
    private string $token;

    #[OA\Property(
        property: 'user',
        description: 'User information',
        ref: '#/components/schemas/Consultant'
    )]
    private object $user;
}

/**
 * Registration request schema
 */
#[OA\Schema(
    schema: 'RegistrationRequest',
    description: 'Registration request model',
    required: ['email', 'password', 'firstName', 'lastName'],
    title: 'RegistrationRequest'
)]
class RegistrationRequest
{
    #[OA\Property(
        property: 'email',
        description: 'User email',
        type: 'string',
        format: 'email',
        example: '<EMAIL>'
    )]
    private string $email;

    #[OA\Property(
        property: 'password',
        description: 'User password',
        type: 'string',
        format: 'password',
        example: 'password123'
    )]
    private string $password;

    #[OA\Property(
        property: 'firstName',
        description: 'First name',
        type: 'string',
        example: 'John'
    )]
    private string $firstName;

    #[OA\Property(
        property: 'lastName',
        description: 'Last name',
        type: 'string',
        example: 'Doe'
    )]
    private string $lastName;

    #[OA\Property(
        property: 'phone',
        description: 'Phone number',
        type: 'string',
        nullable: true,
        example: '+33123456789'
    )]
    private ?string $phone;
}

/**
 * Registration response schema
 */
#[OA\Schema(
    schema: 'RegistrationResponse',
    description: 'Registration response model',
    title: 'RegistrationResponse'
)]
class RegistrationResponse
{
    #[OA\Property(
        property: 'message',
        description: 'Success message',
        type: 'string',
        example: 'User registered successfully'
    )]
    private string $message;

    #[OA\Property(
        property: 'user',
        description: 'User information',
        ref: '#/components/schemas/Consultant'
    )]
    private object $user;
}
