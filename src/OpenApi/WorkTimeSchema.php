<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

/**
 * OpenAPI schema for WorkTime entity
 */
#[OA\Schema(
    schema: 'WorkTime',
    description: 'Work time record model',
    title: 'WorkTime',
    required: ['id', 'consultant', 'date', 'hours', 'remoteWork']
)]
class WorkTimeSchema
{
    #[OA\Property(
        property: 'id',
        description: 'Unique identifier',
        type: 'integer',
        example: 1
    )]
    private int $id;

    #[OA\Property(
        property: 'consultant',
        description: 'The consultant who logged the work time',
        type: 'object',
        properties: [
            new OA\Property(property: 'id', type: 'integer', example: 1),
            new OA\Property(property: 'firstName', type: 'string', example: 'John'),
            new OA\Property(property: 'lastName', type: 'string', example: 'Doe'),
            new OA\Property(property: 'email', type: 'string', format: 'email', example: '<EMAIL>')
        ]
    )]
    private object $consultant;

    #[OA\Property(
        property: 'date',
        description: 'Date of the work time record',
        type: 'string',
        format: 'date',
        example: '2023-06-15'
    )]
    private string $date;

    #[OA\Property(
        property: 'hours',
        description: 'Number of hours worked',
        type: 'number',
        format: 'float',
        example: 7.5
    )]
    private float $hours;

    #[OA\Property(
        property: 'remoteWork',
        description: 'Whether the work was done remotely',
        type: 'boolean',
        example: true
    )]
    private bool $remoteWork;

    #[OA\Property(
        property: 'activity',
        description: 'Activity performed during work time',
        type: 'string',
        nullable: true,
        example: 'Development'
    )]
    private ?string $activity;

    #[OA\Property(
        property: 'comment',
        description: 'Additional comments about the work done',
        type: 'string',
        nullable: true,
        example: 'Worked on the API documentation'
    )]
    private ?string $comment;
}
