<?php

namespace App\OpenApi;

use OpenApi\Attributes as OA;

/**
 * OpenAPI schema for Leave entity
 */
#[OA\Schema(
    schema: 'Leave',
    description: 'Leave request model',
    title: 'Leave'
)]
class LeaveSchema
{
    #[OA\Property(
        property: 'id',
        description: 'Unique identifier',
        type: 'integer',
        example: 1
    )]
    private int $id;

    #[OA\Property(
        property: 'consultant',
        description: 'The consultant who requested the leave',
        ref: '#/components/schemas/Consultant'
    )]
    private object $consultant;

    #[OA\Property(
        property: 'startDate',
        description: 'Start date of the leave',
        type: 'string',
        format: 'date',
        example: '2023-07-10'
    )]
    private string $startDate;

    #[OA\Property(
        property: 'endDate',
        description: 'End date of the leave',
        type: 'string',
        format: 'date',
        example: '2023-07-15'
    )]
    private string $endDate;

    #[OA\Property(
        property: 'days',
        description: 'Number of working days',
        type: 'number',
        format: 'float',
        example: 5.0
    )]
    private float $days;

    #[OA\Property(
        property: 'type',
        description: 'Type of leave',
        type: 'string',
        enum: ['annual', 'sick', 'unpaid', 'other'],
        example: 'annual'
    )]
    private string $type;

    #[OA\Property(
        property: 'reason',
        description: 'Reason for the leave',
        type: 'string',
        nullable: true,
        example: 'Summer vacation'
    )]
    private ?string $reason;

    #[OA\Property(
        property: 'status',
        description: 'Status of the leave request',
        type: 'string',
        enum: ['pending', 'approved', 'rejected'],
        example: 'pending'
    )]
    private string $status;
}
