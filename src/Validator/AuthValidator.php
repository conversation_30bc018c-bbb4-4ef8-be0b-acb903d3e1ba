<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Security\Core\User\UserInterface;

class AuthValidator
{
    public function validateRegistrationData(array $data): array
    {
        $constraints = new Assert\Collection([
            'email' => [
                new Assert\NotBlank(['message' => 'L\'email est requis']),
                new Assert\Email(['message' => 'L\'email n\'est pas valide'])
            ],
            'password' => [
                new Assert\NotBlank(['message' => 'Le mot de passe est requis']),
                new Assert\Length([
                    'min' => 6,
                    'minMessage' => 'Le mot de passe doit contenir au moins {{ limit }} caractères'
                ])
            ]
        ]);

        $validator = Validation::createValidator();
        $violations = $validator->validate($data, $constraints);

        $errors = [];
        foreach ($violations as $violation) {
            $propertyPath = $violation->getPropertyPath();
            $errors[$propertyPath] = $violation->getMessage();
        }

        return $errors;
    }

    public function validateLoginCredentials(?UserInterface $user): bool
    {
        return $user !== null;
    }
}