<?php

namespace App\Repository;

use App\Entity\LeaveCategory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LeaveCategory>
 */
class LeaveCategoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LeaveCategory::class);
    }

    public function save(LeaveCategory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LeaveCategory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find all active categories ordered by sort order
     */
    public function findActiveCategories(): array
    {
        return $this->createQueryBuilder('lc')
            ->andWhere('lc.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('lc.sortOrder', 'ASC')
            ->addOrderBy('lc.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find category by code
     */
    public function findByCode(string $code): ?LeaveCategory
    {
        return $this->createQueryBuilder('lc')
            ->andWhere('lc.code = :code')
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find all categories ordered by sort order
     */
    public function findAllOrdered(): array
    {
        return $this->createQueryBuilder('lc')
            ->orderBy('lc.sortOrder', 'ASC')
            ->addOrderBy('lc.name', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
