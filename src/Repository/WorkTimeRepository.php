<?php

namespace App\Repository;

use App\Entity\WorkTime;
use App\Entity\Consultant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use DateTime;

/**
 * @extends ServiceEntityRepository<WorkTime>
 */
class WorkTimeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WorkTime::class);
    }

    public function save(WorkTime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(WorkTime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByConsultant(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('w')
            ->andWhere('w.consultant = :consultant')
            ->setParameter('consultant', $consultant)
            ->orderBy('w.date', 'DESC');

        if ($startDate) {
            $qb->andWhere('w.date >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('w.date <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function findByDateRange(?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('w')
            ->orderBy('w.date', 'DESC');

        if ($startDate) {
            $qb->andWhere('w.date >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('w.date <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function calculateStatistics(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $workTimes = $this->findByConsultant($consultant, $startDate, $endDate);

        $totalHours = 0;
        $remoteHours = 0;
        $activities = [];
        $daysWorked = count(array_unique(array_map(function($wt) {
            return $wt->getDate()->format('Y-m-d');
        }, $workTimes)));

        foreach ($workTimes as $workTime) {
            $hours = (float) $workTime->getHours();
            $totalHours += $hours;

            if ($workTime->isRemoteWork()) {
                $remoteHours += $hours;
            }

            $activity = $workTime->getActivity() ?: 'Unspecified';
            if (!isset($activities[$activity])) {
                $activities[$activity] = 0;
            }
            $activities[$activity] += $hours;
        }

        $remoteWorkPercentage = $totalHours > 0 ? ($remoteHours / $totalHours) * 100 : 0;
        $averageHoursPerDay = $daysWorked > 0 ? $totalHours / $daysWorked : 0;

        return [
            'totalHours' => $totalHours,
            'remoteHours' => $remoteHours,
            'remotePercentage' => round($remoteWorkPercentage, 1),
            'daysWorked' => $daysWorked,
            'averageHoursPerDay' => round($averageHoursPerDay, 1),
            'activitiesBreakdown' => $activities
        ];
    }
}
