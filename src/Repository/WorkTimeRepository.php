<?php

namespace App\Repository;

use App\Entity\WorkTime;
use App\Entity\Consultant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use DateTime;

/**
 * @extends ServiceEntityRepository<WorkTime>
 */
class WorkTimeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WorkTime::class);
    }

    public function save(WorkTime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(WorkTime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByConsultant(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('w')
            ->andWhere('w.consultant = :consultant')
            ->setParameter('consultant', $consultant)
            ->orderBy('w.startDate', 'DESC');

        if ($startDate) {
            $qb->andWhere('w.endDate >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('w.startDate <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function findByDateRange(?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('w')
            ->orderBy('w.startDate', 'DESC');

        if ($startDate) {
            $qb->andWhere('w.endDate >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('w.startDate <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function calculateStatistics(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $workTimes = $this->findByConsultant($consultant, $startDate, $endDate);

        $totalHours = 0;
        $remoteHours = 0;
        $activities = [];
        $totalDays = 0;
        $periodsCount = count($workTimes);

        foreach ($workTimes as $workTime) {
            $periodHours = (float) $workTime->getTotalHours();
            $totalHours += $periodHours;

            // Calculate remote hours based on percentage
            $remotePercentage = $workTime->getRemoteWorkPercentage() ?? 0;
            $remoteHours += ($periodHours * $remotePercentage / 100);

            // Add days from this period
            $totalDays += $workTime->getPeriodDurationInDays();

            // Process activities
            foreach ($workTime->getActivities() as $activity) {
                $activityName = $activity->getActivityName() ?: 'Unspecified';
                if (!isset($activities[$activityName])) {
                    $activities[$activityName] = 0;
                }
                $activities[$activityName] += (float) $activity->getHours();
            }
        }

        $remoteWorkPercentage = $totalHours > 0 ? ($remoteHours / $totalHours) * 100 : 0;
        $averageHoursPerDay = $totalDays > 0 ? $totalHours / $totalDays : 0;

        return [
            'totalHours' => $totalHours,
            'remoteHours' => round($remoteHours, 1),
            'remotePercentage' => round($remoteWorkPercentage, 1),
            'daysWorked' => $totalDays,
            'averageHoursPerDay' => round($averageHoursPerDay, 1),
            'periodsCount' => $periodsCount,
            'activitiesBreakdown' => $activities
        ];
    }
}
