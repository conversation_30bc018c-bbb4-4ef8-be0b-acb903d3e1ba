<?php

namespace App\Repository;

use App\Entity\Leave;
use App\Entity\Consultant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use DateTime;

/**
 * @extends ServiceEntityRepository<Leave>
 */
class LeaveRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Leave::class);
    }

    public function save(Leave $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Leave $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByConsultant(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('l')
            ->andWhere('l.consultant = :consultant')
            ->setParameter('consultant', $consultant)
            ->orderBy('l.startDate', 'DESC');

        if ($startDate) {
            $qb->andWhere('l.startDate >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('l.endDate <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function findPending(): array
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.status = :status')
            ->setParameter('status', 'pending')
            ->orderBy('l.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function calculateUsedDays(Consultant $consultant, int $year): float
    {
        $startDate = new DateTime($year . '-01-01');
        $endDate = new DateTime($year . '-12-31');

        $leaves = $this->createQueryBuilder('l')
            ->andWhere('l.consultant = :consultant')
            ->andWhere('l.status = :status')
            ->andWhere('l.startDate >= :startDate')
            ->andWhere('l.endDate <= :endDate')
            ->setParameter('consultant', $consultant)
            ->setParameter('status', 'approved')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getResult();

        $totalDays = 0;
        foreach ($leaves as $leave) {
            $totalDays += $leave->getNumberOfDays();
        }

        return $totalDays;
    }
}
