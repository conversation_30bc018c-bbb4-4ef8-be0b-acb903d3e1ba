<?php

namespace App\Repository;

use App\Entity\Project;
use App\Entity\Client;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Project>
 */
class ProjectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Project::class);
    }

    /**
     * Find all active projects
     *
     * @return Project[]
     */
    public function findActive(): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find projects by client
     *
     * @param Client $client
     * @param bool|null $activeOnly
     * @return Project[]
     */
    public function findByClient(Client $client, ?bool $activeOnly = null): array
    {
        $qb = $this->createQueryBuilder('p')
            ->andWhere('p.client = :client')
            ->setParameter('client', $client)
            ->orderBy('p.name', 'ASC');

        if ($activeOnly !== null) {
            $qb->andWhere('p.isActive = :active')
               ->setParameter('active', $activeOnly);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find project by code
     */
    public function findByCode(string $code): ?Project
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.code = :code')
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find currently active projects (based on dates and status)
     *
     * @return Project[]
     */
    public function findCurrentlyActive(): array
    {
        $now = new \DateTime();
        
        return $this->createQueryBuilder('p')
            ->andWhere('p.isActive = :active')
            ->andWhere('(p.startDate IS NULL OR p.startDate <= :now)')
            ->andWhere('(p.endDate IS NULL OR p.endDate >= :now)')
            ->setParameter('active', true)
            ->setParameter('now', $now)
            ->orderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search projects by name or code
     *
     * @param string $search
     * @return Project[]
     */
    public function search(string $search): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.name LIKE :search OR p.code LIKE :search')
            ->setParameter('search', '%' . $search . '%')
            ->orderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Count projects by client
     */
    public function countByClient(Client $client): int
    {
        return $this->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->andWhere('p.client = :client')
            ->setParameter('client', $client)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
