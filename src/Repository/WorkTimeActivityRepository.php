<?php

namespace App\Repository;

use App\Entity\WorkTimeActivity;
use App\Entity\WorkTime;
use App\Entity\Client;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<WorkTimeActivity>
 *
 * @method WorkTimeActivity|null find($id, $lockMode = null, $lockVersion = null)
 * @method WorkTimeActivity|null findOneBy(array $criteria, array $orderBy = null)
 * @method WorkTimeActivity[]    findAll()
 * @method WorkTimeActivity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WorkTimeActivityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WorkTimeActivity::class);
    }

    /**
     * Find activities by work time
     */
    public function findByWorkTime(WorkTime $workTime): array
    {
        return $this->createQueryBuilder('wta')
            ->andWhere('wta.workTime = :workTime')
            ->setParameter('workTime', $workTime)
            ->orderBy('wta.hours', 'DESC')
            ->getQuery()
            ->getResult();
    }



    /**
     * Find billable activities by work time
     */
    public function findBillableByWorkTime(WorkTime $workTime): array
    {
        return $this->createQueryBuilder('wta')
            ->andWhere('wta.workTime = :workTime')
            ->andWhere('wta.isBillable = :billable')
            ->setParameter('workTime', $workTime)
            ->setParameter('billable', true)
            ->orderBy('wta.hours', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total hours for a work time
     */
    public function getTotalHoursForWorkTime(WorkTime $workTime): float
    {
        $result = $this->createQueryBuilder('wta')
            ->select('SUM(wta.hours) as totalHours')
            ->andWhere('wta.workTime = :workTime')
            ->setParameter('workTime', $workTime)
            ->getQuery()
            ->getSingleResult();

        return (float) ($result['totalHours'] ?? 0.0);
    }

    /**
     * Find activities by activity name pattern
     */
    public function findByActivityNamePattern(string $pattern): array
    {
        return $this->createQueryBuilder('wta')
            ->andWhere('wta.activityName LIKE :pattern')
            ->setParameter('pattern', '%' . $pattern . '%')
            ->orderBy('wta.activityName', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get unique activity names for autocomplete
     */
    public function getUniqueActivityNames(): array
    {
        $result = $this->createQueryBuilder('wta')
            ->select('DISTINCT wta.activityName')
            ->orderBy('wta.activityName', 'ASC')
            ->getQuery()
            ->getResult();

        return array_column($result, 'activityName');
    }


}
