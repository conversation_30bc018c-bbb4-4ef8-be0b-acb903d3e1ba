<?php

namespace App\Repository;

use App\Entity\Trip;
use App\Entity\Consultant;
use App\Entity\Client;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use DateTime;

/**
 * @extends ServiceEntityRepository<Trip>
 */
class TripRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Trip::class);
    }

    public function save(Trip $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Trip $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find trips by consultant
     */
    public function findByConsultant(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('t')
            ->andWhere('t.consultant = :consultant')
            ->setParameter('consultant', $consultant)
            ->orderBy('t.startDate', 'DESC');

        if ($startDate) {
            $qb->andWhere('t.endDate >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('t.startDate <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find trips by client
     */
    public function findByClient(Client $client, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('t')
            ->andWhere('t.client = :client')
            ->setParameter('client', $client)
            ->orderBy('t.startDate', 'DESC');

        if ($startDate) {
            $qb->andWhere('t.endDate >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('t.startDate <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find current trips (ongoing)
     */
    public function findCurrent(): array
    {
        $today = new DateTime();
        
        return $this->createQueryBuilder('t')
            ->andWhere('t.startDate <= :today')
            ->andWhere('t.endDate >= :today')
            ->setParameter('today', $today)
            ->orderBy('t.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find upcoming trips
     */
    public function findUpcoming(int $days = 30): array
    {
        $today = new DateTime();
        $futureDate = new DateTime('+' . $days . ' days');
        
        return $this->createQueryBuilder('t')
            ->andWhere('t.startDate > :today')
            ->andWhere('t.startDate <= :futureDate')
            ->setParameter('today', $today)
            ->setParameter('futureDate', $futureDate)
            ->orderBy('t.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
