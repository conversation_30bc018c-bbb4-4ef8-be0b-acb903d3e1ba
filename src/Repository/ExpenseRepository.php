<?php

namespace App\Repository;

use App\Entity\Expense;
use App\Entity\Consultant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use DateTime;

/**
 * @extends ServiceEntityRepository<Expense>
 */
class ExpenseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Expense::class);
    }

    public function save(Expense $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Expense $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByConsultant(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('e')
            ->andWhere('e.consultant = :consultant')
            ->setParameter('consultant', $consultant)
            ->orderBy('e.date', 'DESC');

        if ($startDate) {
            $qb->andWhere('e.date >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('e.date <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function findNonValidated(): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.validated = :validated')
            ->setParameter('validated', false)
            ->orderBy('e.date', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByDateRange(?DateTime $startDate = null, ?DateTime $endDate = null): array
    {
        $qb = $this->createQueryBuilder('e')
            ->orderBy('e.date', 'DESC');

        if ($startDate) {
            $qb->andWhere('e.date >= :startDate')
                ->setParameter('startDate', $startDate);
        }

        if ($endDate) {
            $qb->andWhere('e.date <= :endDate')
                ->setParameter('endDate', $endDate);
        }

        return $qb->getQuery()->getResult();
    }

    public function calculateTotal(Consultant $consultant, ?DateTime $startDate = null, ?DateTime $endDate = null): float
    {
        $expenses = $this->findByConsultant($consultant, $startDate, $endDate);

        $total = 0;
        foreach ($expenses as $expense) {
            if ($expense->isValidated()) {
                $total += (float) $expense->getAmountEur();
            }
        }

        return $total;
    }
}
