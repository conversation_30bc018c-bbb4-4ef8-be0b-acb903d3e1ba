<?php

namespace App\Command;

use App\Entity\Client;
use App\Entity\ExpenseCategory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:seed-expense-data',
    description: 'Seed the database with sample clients and expense categories',
)]
class SeedExpenseDataCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Seeding Expense Management Data');

        // Create sample clients
        $this->createClients($io);

        // Create expense categories
        $this->createExpenseCategories($io);

        $this->entityManager->flush();

        $io->success('Sample data has been successfully created!');

        return Command::SUCCESS;
    }

    private function createClients(SymfonyStyle $io): void
    {
        $io->section('Creating sample clients...');

        $clientsData = [
            ['name' => 'Acme Corporation', 'code' => 'ACME', 'address' => '123 Business St, New York, NY', 'email' => '<EMAIL>', 'website' => 'https://acme.com'],
            ['name' => 'TechStart Inc', 'code' => 'TECH', 'address' => '456 Innovation Ave, San Francisco, CA', 'email' => '<EMAIL>', 'website' => 'https://techstart.com'],
            ['name' => 'Global Solutions Ltd', 'code' => 'GLOBAL', 'address' => '789 Enterprise Blvd, London, UK', 'email' => '<EMAIL>', 'website' => 'https://globalsolutions.co.uk'],
            ['name' => 'Swiss Finance AG', 'code' => 'SWISS', 'address' => 'Bahnhofstrasse 1, Zurich, Switzerland', 'email' => '<EMAIL>', 'website' => 'https://swissfinance.ch'],
            ['name' => 'Nordic Consulting', 'code' => 'NORDIC', 'address' => 'Storgatan 15, Stockholm, Sweden', 'email' => '<EMAIL>', 'website' => 'https://nordicconsulting.se']
        ];

        foreach ($clientsData as $clientData) {
            // Check if client already exists
            $existingClient = $this->entityManager->getRepository(Client::class)->findByCode($clientData['code']);
            if ($existingClient) {
                $io->text("Client {$clientData['name']} already exists, skipping...");
                continue;
            }

            $client = new Client();
            $client->setName($clientData['name']);
            $client->setCode($clientData['code']);
            $client->setAddress($clientData['address']);
            $client->setEmail($clientData['email']);
            $client->setWebsite($clientData['website']);
            $client->setIsActive(true);

            $this->entityManager->persist($client);
            $io->text("Created client: {$clientData['name']}");
        }
    }

    private function createExpenseCategories(SymfonyStyle $io): void
    {
        $io->section('Creating expense categories...');

        $categoriesData = [
            // Root categories
            ['name' => 'Transportation', 'code' => 'TRANSPORT', 'description' => 'All transportation related expenses', 'requiresReceipt' => true, 'parent' => null],
            ['name' => 'Accommodation', 'code' => 'ACCOMMODATION', 'description' => 'Hotel and lodging expenses', 'requiresReceipt' => true, 'parent' => null],
            ['name' => 'Meals & Entertainment', 'code' => 'MEALS', 'description' => 'Food and entertainment expenses', 'requiresReceipt' => true, 'parent' => null],
            ['name' => 'Office & Supplies', 'code' => 'OFFICE', 'description' => 'Office supplies and equipment', 'requiresReceipt' => true, 'parent' => null],
            ['name' => 'Communication', 'code' => 'COMMUNICATION', 'description' => 'Phone, internet, and communication expenses', 'requiresReceipt' => false, 'parent' => null],
            ['name' => 'Other', 'code' => 'OTHER', 'description' => 'Miscellaneous expenses', 'requiresReceipt' => false, 'parent' => null],
        ];

        $parentCategories = [];

        // Create root categories first
        foreach ($categoriesData as $categoryData) {
            $existingCategory = $this->entityManager->getRepository(ExpenseCategory::class)->findByCode($categoryData['code']);
            if ($existingCategory) {
                $io->text("Category {$categoryData['name']} already exists, skipping...");
                $parentCategories[$categoryData['code']] = $existingCategory;
                continue;
            }

            $category = new ExpenseCategory();
            $category->setName($categoryData['name']);
            $category->setCode($categoryData['code']);
            $category->setDescription($categoryData['description']);
            $category->setRequiresReceipt($categoryData['requiresReceipt']);
            $category->setIsActive(true);

            $this->entityManager->persist($category);
            $parentCategories[$categoryData['code']] = $category;
            $io->text("Created category: {$categoryData['name']}");
        }

        // Create subcategories
        $subcategoriesData = [
            // Transportation subcategories
            ['name' => 'Flight Tickets', 'code' => 'FLIGHT', 'description' => 'Airline tickets and fees', 'requiresReceipt' => true, 'parent' => 'TRANSPORT'],
            ['name' => 'Train Tickets', 'code' => 'TRAIN', 'description' => 'Railway transportation', 'requiresReceipt' => true, 'parent' => 'TRANSPORT'],
            ['name' => 'Taxi & Rideshare', 'code' => 'TAXI', 'description' => 'Taxi, Uber, Lyft expenses', 'requiresReceipt' => true, 'parent' => 'TRANSPORT'],
            ['name' => 'Car Rental', 'code' => 'CAR_RENTAL', 'description' => 'Vehicle rental expenses', 'requiresReceipt' => true, 'parent' => 'TRANSPORT'],
            ['name' => 'Parking', 'code' => 'PARKING', 'description' => 'Parking fees and tolls', 'requiresReceipt' => false, 'parent' => 'TRANSPORT'],

            // Accommodation subcategories
            ['name' => 'Hotel', 'code' => 'HOTEL', 'description' => 'Hotel accommodation', 'requiresReceipt' => true, 'parent' => 'ACCOMMODATION'],
            ['name' => 'Airbnb', 'code' => 'AIRBNB', 'description' => 'Short-term rental accommodation', 'requiresReceipt' => true, 'parent' => 'ACCOMMODATION'],

            // Meals subcategories
            ['name' => 'Business Meals', 'code' => 'BUSINESS_MEALS', 'description' => 'Client meals and business dining', 'requiresReceipt' => true, 'parent' => 'MEALS'],
            ['name' => 'Travel Meals', 'code' => 'TRAVEL_MEALS', 'description' => 'Meals during business travel', 'requiresReceipt' => true, 'parent' => 'MEALS'],

            // Office subcategories
            ['name' => 'Stationery', 'code' => 'STATIONERY', 'description' => 'Pens, paper, office supplies', 'requiresReceipt' => true, 'parent' => 'OFFICE'],
            ['name' => 'Software', 'code' => 'SOFTWARE', 'description' => 'Software licenses and subscriptions', 'requiresReceipt' => true, 'parent' => 'OFFICE'],

            // Communication subcategories
            ['name' => 'Mobile Phone', 'code' => 'MOBILE', 'description' => 'Mobile phone expenses', 'requiresReceipt' => false, 'parent' => 'COMMUNICATION'],
            ['name' => 'Internet', 'code' => 'INTERNET', 'description' => 'Internet and data expenses', 'requiresReceipt' => false, 'parent' => 'COMMUNICATION'],
        ];

        foreach ($subcategoriesData as $subcategoryData) {
            $existingCategory = $this->entityManager->getRepository(ExpenseCategory::class)->findByCode($subcategoryData['code']);
            if ($existingCategory) {
                $io->text("Subcategory {$subcategoryData['name']} already exists, skipping...");
                continue;
            }

            $subcategory = new ExpenseCategory();
            $subcategory->setName($subcategoryData['name']);
            $subcategory->setCode($subcategoryData['code']);
            $subcategory->setDescription($subcategoryData['description']);
            $subcategory->setRequiresReceipt($subcategoryData['requiresReceipt']);
            $subcategory->setIsActive(true);
            $subcategory->setParentCategory($parentCategories[$subcategoryData['parent']]);

            $this->entityManager->persist($subcategory);
            $io->text("Created subcategory: {$subcategoryData['name']} under {$subcategoryData['parent']}");
        }
    }
}
