<?php

namespace App\Command;

use App\Entity\Consultant;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsCommand(
    name: 'app:create-test-user',
    description: 'Creates a test user for API testing',
)]
class CreateTestUserCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly UserPasswordHasherInterface $passwordHasher
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Check if test user already exists
        $existingUser = $this->entityManager->getRepository(Consultant::class)->findOneBy(['email' => '<EMAIL>']);

        if ($existingUser) {
            $io->note('Test user already exists');
            return Command::SUCCESS;
        }

        try {
            // Create consultant
            $consultant = new Consultant();
            $consultant->setEmail('<EMAIL>');
            $consultant->setPassword($this->passwordHasher->hashPassword($consultant, 'password'));

            // Set user information
            $consultant->setLastName('Test');
            $consultant->setFirstName('User');
            $consultant->setPhone('+33123456789');

            $consultant->setIsAdmin(false);
            $consultant->setRoles(['ROLE_USER']);

            $this->entityManager->persist($consultant);
            $this->entityManager->flush();

            $io->success('Test user created successfully');

            // Check if admin already exists
            $existingAdmin = $this->entityManager->getRepository(Consultant::class)->findOneBy(['email' => '<EMAIL>']);

            if ($existingAdmin) {
                $io->note('Admin user already exists');
                return Command::SUCCESS;
            }

            // Create admin user
            $admin = new Consultant();
            $admin->setEmail('<EMAIL>');
            $admin->setPassword($this->passwordHasher->hashPassword($admin, 'password'));

            // Set user information
            $admin->setLastName('Admin');
            $admin->setFirstName('User');
            $admin->setPhone('+33987654321');

            $admin->setIsAdmin(true);
            $admin->setRoles(['ROLE_USER', 'ROLE_ADMIN']);

            $this->entityManager->persist($admin);
            $this->entityManager->flush();

            $io->success('Admin user created successfully');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error('Failed to create test users: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
