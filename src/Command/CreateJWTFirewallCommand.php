<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use S<PERSON>fony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\Filesystem\Filesystem;

/**
 * Command to create or update JWT firewall configuration
 */
#[AsCommand(
    name: 'app:create-jwt-firewall',
    description: 'Creates or updates JWT firewall configuration in security.yaml',
)]
class CreateJWTFirewallCommand extends Command
{
    private const CONFIG_PATH = 'config/packages/security.yaml';

    public function __construct(
        private readonly Filesystem $filesystem
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('firewall-name', InputArgument::REQUIRED, 'Name of the firewall to create')
            ->addOption('pattern', null, InputOption::VALUE_REQUIRED, 'URL pattern for the firewall', '^/api')
            ->addOption('provider', null, InputOption::VALUE_REQUIRED, 'User provider for the firewall', 'app_user_provider')
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'Force overwrite if firewall already exists')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $firewallName = $input->getArgument('firewall-name');
        $pattern = $input->getOption('pattern');
        $provider = $input->getOption('provider');
        $force = $input->getOption('force');

        // Check if config file exists
        if (!$this->filesystem->exists(self::CONFIG_PATH)) {
            $io->error('Security configuration file not found at: ' . self::CONFIG_PATH);
            return Command::FAILURE;
        }

        // Load current configuration
        try {
            $securityConfig = Yaml::parseFile(self::CONFIG_PATH);
        } catch (\Exception $e) {
            $io->error('Failed to parse security configuration: ' . $e->getMessage());
            return Command::FAILURE;
        }

        // Check if firewalls section exists
        if (!isset($securityConfig['security']['firewalls'])) {
            $io->error('Invalid security configuration: "firewalls" section not found');
            return Command::FAILURE;
        }

        // Check if firewall already exists
        if (isset($securityConfig['security']['firewalls'][$firewallName]) && !$force) {
            $io->error(sprintf('Firewall "%s" already exists. Use --force to overwrite.', $firewallName));
            return Command::FAILURE;
        }

        // Create JWT firewall configuration
        $securityConfig['security']['firewalls'][$firewallName] = [
            'pattern' => $pattern,
            'stateless' => true,
            'provider' => $provider,
            'jwt' => [
                'secret' => '%env(JWT_SECRET_KEY)%',
                'algorithm' => 'HS256',
                'token_extractors' => [
                    'authorization_header' => [
                        'prefix' => 'Bearer',
                        'name' => 'Authorization',
                    ],
                ],
            ],
        ];

        // Save updated configuration
        try {
            $yaml = Yaml::dump($securityConfig, 6);
            $this->filesystem->dumpFile(self::CONFIG_PATH, $yaml);
        } catch (\Exception $e) {
            $io->error('Failed to save security configuration: ' . $e->getMessage());
            return Command::FAILURE;
        }

        $io->success(sprintf('JWT firewall "%s" has been created successfully.', $firewallName));
        $io->info('Make sure you have JWT_SECRET_KEY defined in your .env file.');

        return Command::SUCCESS;
    }
}
