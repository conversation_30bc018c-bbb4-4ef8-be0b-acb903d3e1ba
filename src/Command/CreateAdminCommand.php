<?php

namespace App\Command;

use App\Entity\Consultant;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Command to create an administrator user
 */
#[AsCommand(
    name: 'app:create-admin',
    description: 'Creates a new administrator user',
)]
class CreateAdminCommand extends Command
{
    /**
     * Default admin role
     */
    private const ROLE_ADMIN = 'ROLE_ADMIN';

    /**
     * Default user role
     */
    private const ROLE_USER = 'ROLE_USER';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly UserPasswordHasherInterface $passwordHasher,
        private readonly ?ValidatorInterface $validator = null
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('email', InputArgument::REQUIRED, 'The email of the administrator')
            ->addArgument('password', InputArgument::REQUIRED, 'The password of the administrator')
            ->addOption('last-name', 'l', InputOption::VALUE_REQUIRED, 'The last name of the administrator', 'Admin')
            ->addOption('first-name', 'f', InputOption::VALUE_REQUIRED, 'The first name of the administrator', 'Admin')
            ->addOption('phone', 'p', InputOption::VALUE_OPTIONAL, 'The phone number of the administrator')
            ->addOption('force', null, InputOption::VALUE_NONE, 'Force creation even if the user already exists')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $email = $input->getArgument('email');
        $password = $input->getArgument('password');
        $lastName = $input->getOption('last-name');
        $firstName = $input->getOption('first-name');
        $phone = $input->getOption('phone');
        $force = $input->getOption('force');

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $io->error('Invalid email format');
            return Command::INVALID;
        }

        // Check if user already exists
        $existingUser = $this->entityManager->getRepository(Consultant::class)->findOneBy(['email' => $email]);
        if ($existingUser) {
            if (!$force) {
                $io->error(sprintf('User with email "%s" already exists. Use --force to update it.', $email));
                return Command::FAILURE;
            }

            $io->note(sprintf('Updating existing user with email "%s"', $email));
            $admin = $existingUser;
        } else {
            $io->note(sprintf('Creating new administrator with email "%s"', $email));
            $admin = new Consultant();
            $admin->setEmail($email);
        }

        // Set or update user data
        $admin->setPassword($this->passwordHasher->hashPassword($admin, $password));
        $admin->setLastName($lastName);
        $admin->setFirstName($firstName);

        if ($phone !== null) {
            $admin->setPhone($phone);
        }

        $admin->setIsAdmin(true);
        $admin->setRoles([self::ROLE_USER, self::ROLE_ADMIN]);

        // Validate the entity if validator is available
        if ($this->validator) {
            $errors = $this->validator->validate($admin);
            if (count($errors) > 0) {
                $errorMessages = [];
                foreach ($errors as $error) {
                    $errorMessages[] = $error->getMessage();
                }
                $io->error('Validation failed: ' . implode(', ', $errorMessages));
                return Command::FAILURE;
            }
        }

        // Persist the entity
        try {
            if (!$existingUser) {
                $this->entityManager->persist($admin);
            }
            $this->entityManager->flush();
        } catch (\Exception $e) {
            $io->error('Failed to save administrator: ' . $e->getMessage());
            return Command::FAILURE;
        }

        $io->success(sprintf('Administrator "%s %s" (%s) has been %s successfully.',
            $firstName,
            $lastName,
            $email,
            $existingUser ? 'updated' : 'created'
        ));

        return Command::SUCCESS;
    }
}
