{"info": {"_postman_id": "f6g7h8i9-j0k1-2345-lmno-pq6789012345", "name": "MBS - Congés", "description": "Collection pour tester les endpoints de gestion des congés de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Conges", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges", "host": ["{{base_url}}"], "path": ["api", "conges"]}, "description": "Récupère la liste de tous les congés (admin uniquement)"}, "response": []}, {"name": "Get Pending Conges", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges/en-attente", "host": ["{{base_url}}"], "path": ["api", "conges", "en-attente"]}, "description": "Récupère la liste des congés en attente (admin uniquement)"}, "response": []}, {"name": "Get Conge", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges/1", "host": ["{{base_url}}"], "path": ["api", "conges", "1"]}, "description": "Récupère les détails d'un congé spécifique"}, "response": []}, {"name": "Get Conges By Consultant", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges/consultant/1", "host": ["{{base_url}}"], "path": ["api", "conges", "consultant", "1"], "query": [{"key": "debut", "value": "2023-05-01", "disabled": true}, {"key": "fin", "value": "2023-05-31", "disabled": true}]}, "description": "Récupère les congés d'un consultant spécifique"}, "response": []}, {"name": "Create Conge", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"consultantId\": 1,\n    \"dateDebut\": \"2023-07-10\",\n    \"dateFin\": \"2023-07-14\",\n    \"nombreJours\": 5,\n    \"type\": \"congés payés\",\n    \"commentaire\": \"Vacances d'été\"\n}"}, "url": {"raw": "{{base_url}}/api/conges", "host": ["{{base_url}}"], "path": ["api", "conges"]}, "description": "Crée une nouvelle demande de congé"}, "response": []}, {"name": "Update Conge", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"dateDebut\": \"2023-07-17\",\n    \"dateFin\": \"2023-07-21\",\n    \"nombreJours\": 5\n}"}, "url": {"raw": "{{base_url}}/api/conges/1", "host": ["{{base_url}}"], "path": ["api", "conges", "1"]}, "description": "Modifie une demande de congé existante"}, "response": []}, {"name": "Delete Conge", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges/1", "host": ["{{base_url}}"], "path": ["api", "conges", "1"]}, "description": "Supprime une demande de congé"}, "response": []}, {"name": "Approve Conge", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges/1/approuver", "host": ["{{base_url}}"], "path": ["api", "conges", "1", "approuver"]}, "description": "Approuve une demande de congé (admin uniquement)"}, "response": []}, {"name": "Reject Conge", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"motif\": \"P<PERSON>riode de forte activité\"\n}"}, "url": {"raw": "{{base_url}}/api/conges/1/refuser", "host": ["{{base_url}}"], "path": ["api", "conges", "1", "refuser"]}, "description": "Refuse une demande de congé (admin uniquement)"}, "response": []}, {"name": "Get Conges Solde", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/conges/solde/1", "host": ["{{base_url}}"], "path": ["api", "conges", "solde", "1"]}, "description": "Récupère le solde de congés d'un consultant"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}