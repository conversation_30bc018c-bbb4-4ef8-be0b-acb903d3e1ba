{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "MBS - Authentication", "description": "Collection pour tester les endpoints d'authentification de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/register", "host": ["{{base_url}}"], "path": ["api", "register"]}, "description": "Inscription d'un nouvel utilisateur"}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData.token) {", "    pm.environment.set(\"jwt_token\", jsonData.token);", "    console.log(\"Token JWT sauvegardé dans la variable d'environnement\");", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "Connexion d'un utilisateur ou consultant"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}