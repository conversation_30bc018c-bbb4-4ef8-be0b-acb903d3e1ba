{"info": {"_postman_id": "g7h8i9j0-k1l2-3456-mnop-qr7890123456", "name": "MBS - Dashboard", "description": "Collection pour tester les endpoints de tableau de bord de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Consultant Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/consultant/1", "host": ["{{base_url}}"], "path": ["api", "dashboard", "consultant", "1"], "query": [{"key": "debut", "value": "2023-05-01", "disabled": true}, {"key": "fin", "value": "2023-05-31", "disabled": true}]}, "description": "Récupère le tableau de bord d'un consultant spécifique"}, "response": []}, {"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/admin", "host": ["{{base_url}}"], "path": ["api", "dashboard", "admin"], "query": [{"key": "debut", "value": "2023-05-01", "disabled": true}, {"key": "fin", "value": "2023-05-31", "disabled": true}]}, "description": "Récupère le tableau de bord administrateur (admin uniquement)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}