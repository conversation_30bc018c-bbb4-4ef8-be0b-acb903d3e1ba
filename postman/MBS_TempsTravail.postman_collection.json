{"info": {"_postman_id": "d4e5f6g7-h8i9-0123-jklm-no4567890123", "name": "MBS - Temps de Travail", "description": "Collection pour tester les endpoints de gestion du temps de travail de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Temps Travail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/temps-travail", "host": ["{{base_url}}"], "path": ["api", "temps-travail"]}, "description": "Récupère la liste de tous les temps de travail (admin uniquement)"}, "response": []}, {"name": "Get Temps Travail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/temps-travail/1", "host": ["{{base_url}}"], "path": ["api", "temps-travail", "1"]}, "description": "Récupère les détails d'un temps de travail spécifique"}, "response": []}, {"name": "Get Temps Travail By Consultant", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/temps-travail/consultant/1", "host": ["{{base_url}}"], "path": ["api", "temps-travail", "consultant", "1"], "query": [{"key": "debut", "value": "2023-05-01", "disabled": true}, {"key": "fin", "value": "2023-05-31", "disabled": true}]}, "description": "Récupère les temps de travail d'un consultant spécifique"}, "response": []}, {"name": "Create Temps Travail", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"consultantId\": 1,\n    \"date\": \"2023-05-15\",\n    \"heures\": 7.5,\n    \"teletravail\": true,\n    \"activite\": \"Développement\",\n    \"commentaire\": \"Travail sur le projet MBS\"\n}"}, "url": {"raw": "{{base_url}}/api/temps-travail", "host": ["{{base_url}}"], "path": ["api", "temps-travail"]}, "description": "Crée un nouveau temps de travail"}, "response": []}, {"name": "Update Temps Travail", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"heures\": 8.0,\n    \"teletravail\": false,\n    \"commentaire\": \"Mise à jour du commentaire\"\n}"}, "url": {"raw": "{{base_url}}/api/temps-travail/1", "host": ["{{base_url}}"], "path": ["api", "temps-travail", "1"]}, "description": "Modifie un temps de travail existant"}, "response": []}, {"name": "Delete Temps Travail", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/temps-travail/1", "host": ["{{base_url}}"], "path": ["api", "temps-travail", "1"]}, "description": "Supprime un temps de travail"}, "response": []}, {"name": "Get Temps Travail Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/temps-travail/statistiques/1?debut=2023-05-01&fin=2023-05-31", "host": ["{{base_url}}"], "path": ["api", "temps-travail", "statistiques", "1"], "query": [{"key": "debut", "value": "2023-05-01"}, {"key": "fin", "value": "2023-05-31"}]}, "description": "Récupère les statistiques de temps de travail d'un consultant"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}