{"info": {"_postman_id": "c3d4e5f6-g7h8-9012-ijkl-mn3456789012", "name": "MBS - Entreprise", "description": "Collection pour tester les endpoints de gestion de l'entreprise de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Entreprise", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/entreprise", "host": ["{{base_url}}"], "path": ["api", "entreprise"]}, "description": "Récupère les informations de l'entreprise"}, "response": []}, {"name": "Update Entreprise", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nom\": \"MBS Consulting\",\n    \"adresse\": \"123 Avenue des Champs-Élysées, Paris\",\n    \"telephone\": \"0123456789\",\n    \"email\": \"<EMAIL>\",\n    \"siteWeb\": \"https://mbs-consulting.com\",\n    \"siret\": \"12345678900012\",\n    \"joursCongesAnnuels\": 25,\n    \"heuresTravailJour\": 7.5,\n    \"pourcentageTeletravailMax\": 60.0\n}"}, "url": {"raw": "{{base_url}}/api/entreprise", "host": ["{{base_url}}"], "path": ["api", "entreprise"]}, "description": "Modifie les informations de l'entreprise (admin uniquement)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}