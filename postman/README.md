# Collections Postman pour MBS API

Ce dossier contient des collections Postman pour tester l'API MBS. Les collections sont organisées par module pour faciliter les tests.

## Contenu

1. **MBS_Authentication.postman_collection.json** - Endpoints d'authentification
2. **MBS_Consultants.postman_collection.json** - Gestion des consultants
3. **MBS_Entreprise.postman_collection.json** - Gestion de l'entreprise
4. **MBS_TempsTravail.postman_collection.json** - Gestion du temps de travail
5. **MBS_Frais.postman_collection.json** - Gestion des frais
6. **MBS_Conges.postman_collection.json** - Gestion des congés
7. **MBS_Dashboard.postman_collection.json** - Tableaux de bord
8. **MBS_Environment.postman_environment.json** - Variables d'environnement

## Installation

1. Téléchargez et installez [Postman](https://www.postman.com/downloads/)
2. Ouvrez Postman
3. Importez l'environnement :
   - Cliquez sur "Import" en haut à gauche
   - Sélectionnez le fichier `MBS_Environment.postman_environment.json`
   - Cliquez sur "Import"
4. Importez les collections :
   - Cliquez sur "Import" en haut à gauche
   - Sélectionnez tous les fichiers `.postman_collection.json`
   - Cliquez sur "Import"

## Configuration

1. Sélectionnez l'environnement "MBS Environment" dans le menu déroulant en haut à droite
2. Vérifiez que la variable `base_url` est correctement configurée (par défaut : `https://localhost:8012`)

## Utilisation

1. Commencez par créer un administrateur en utilisant la commande CLI :
   ```bash
   php bin/console app:create-admin <EMAIL> password Admin Admin
   ```

2. Utilisez la requête "Login" dans la collection "MBS - Authentication" pour vous connecter :
   - Modifiez le corps de la requête avec les identifiants de l'administrateur
   - Envoyez la requête
   - Le token JWT sera automatiquement sauvegardé dans la variable d'environnement `jwt_token`

3. Vous pouvez maintenant utiliser toutes les autres requêtes qui utiliseront automatiquement le token JWT pour l'authentification

## Notes

- Les requêtes sont préconfigurées avec des exemples de données
- Modifiez les corps des requêtes selon vos besoins
- Les IDs dans les URLs (comme `/api/consultants/1`) doivent être remplacés par les IDs réels de vos entités
- Certaines requêtes nécessitent des droits d'administrateur
