{"info": {"_postman_id": "b2c3d4e5-f6g7-8901-hijk-lm2345678901", "name": "MBS - Consultants", "description": "Collection pour tester les endpoints de gestion des consultants de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Consultants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/consultants", "host": ["{{base_url}}"], "path": ["api", "consultants"]}, "description": "Récupère la liste de tous les consultants (admin uniquement)"}, "response": []}, {"name": "Get Consultant", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/consultants/1", "host": ["{{base_url}}"], "path": ["api", "consultants", "1"]}, "description": "Récupère les détails d'un consultant spécifique"}, "response": []}, {"name": "Get Current Consultant", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/consultants/me", "host": ["{{base_url}}"], "path": ["api", "consultants", "me"]}, "description": "Récupère les informations du consultant connecté"}, "response": []}, {"name": "Create Consultant", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"nom\": \"<PERSON>\",\n    \"prenom\": \"<PERSON>\",\n    \"telephone\": \"0987654321\",\n    \"isAdmin\": false\n}"}, "url": {"raw": "{{base_url}}/api/consultants", "host": ["{{base_url}}"], "path": ["api", "consultants"]}, "description": "<PERSON><PERSON>e un nouveau consultant (admin uniquement)"}, "response": []}, {"name": "Update Consultant", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"telephone\": \"0123456789\",\n    \"isAdmin\": true\n}"}, "url": {"raw": "{{base_url}}/api/consultants/1", "host": ["{{base_url}}"], "path": ["api", "consultants", "1"]}, "description": "Modifie les informations d'un consultant existant"}, "response": []}, {"name": "Delete Consultant", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/consultants/2", "host": ["{{base_url}}"], "path": ["api", "consultants", "2"]}, "description": "Supprime un consultant (admin uniquement)"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}