{"info": {"_postman_id": "e5f6g7h8-i9j0-1234-klmn-op5678901234", "name": "MBS - Frais", "description": "Collection pour tester les endpoints de gestion des frais de l'API MBS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Frais", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais", "host": ["{{base_url}}"], "path": ["api", "frais"]}, "description": "Récupère la liste de tous les frais (admin uniquement)"}, "response": []}, {"name": "Get Non-Validated Frais", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais/non-valides", "host": ["{{base_url}}"], "path": ["api", "frais", "non-valides"]}, "description": "Récupère la liste des frais non validés (admin uniquement)"}, "response": []}, {"name": "<PERSON>ais", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais/1", "host": ["{{base_url}}"], "path": ["api", "frais", "1"]}, "description": "Récupère les détails d'un frais spécifique"}, "response": []}, {"name": "Get Frais By Consultant", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais/consultant/1", "host": ["{{base_url}}"], "path": ["api", "frais", "consultant", "1"], "query": [{"key": "debut", "value": "2023-05-01", "disabled": true}, {"key": "fin", "value": "2023-05-31", "disabled": true}]}, "description": "Récupère les frais d'un consultant spécifique"}, "response": []}, {"name": "C<PERSON> <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"consultantId\": 1,\n    \"date\": \"2023-05-15\",\n    \"description\": \"Repas client\",\n    \"montant\": 45.50,\n    \"devise\": \"EUR\",\n    \"justificatif\": \"facture-123.pdf\"\n}"}, "url": {"raw": "{{base_url}}/api/frais", "host": ["{{base_url}}"], "path": ["api", "frais"]}, "description": "Crée un nouveau frais"}, "response": []}, {"name": "Update Frais", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"Repas client - mise à jour\",\n    \"montant\": 50.00\n}"}, "url": {"raw": "{{base_url}}/api/frais/1", "host": ["{{base_url}}"], "path": ["api", "frais", "1"]}, "description": "Modifie un frais existant"}, "response": []}, {"name": "Delete Frais", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais/1", "host": ["{{base_url}}"], "path": ["api", "frais", "1"]}, "description": "Supprime un frais"}, "response": []}, {"name": "Validate <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais/1/valider", "host": ["{{base_url}}"], "path": ["api", "frais", "1", "valider"]}, "description": "<PERSON>ide un frais (admin uniquement)"}, "response": []}, {"name": "Get Frais Total", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/frais/total/1?debut=2023-05-01&fin=2023-05-31", "host": ["{{base_url}}"], "path": ["api", "frais", "total", "1"], "query": [{"key": "debut", "value": "2023-05-01"}, {"key": "fin", "value": "2023-05-31"}]}, "description": "Récupère le total des frais d'un consultant"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://localhost:8012", "type": "string"}]}